<?php

namespace App\Models;

use CodeIgniter\Model;

class PasswordResetModel extends Model
{
    protected $table            = 'password_resets';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['email', 'token', 'expires_at'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'email' => 'required|valid_email',
        'token' => 'required|min_length[32]',
        'expires_at' => 'required'
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Create a password reset token for the given email
     */
    public function createToken($email)
    {
        // Delete any existing tokens for this email
        $this->where('email', $email)->delete();

        // Generate a secure token
        $token = bin2hex(random_bytes(32));
        
        // Set expiration time (1 hour from now)
        $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));

        $data = [
            'email' => $email,
            'token' => $token,
            'expires_at' => $expiresAt
        ];

        $this->insert($data);

        return $token;
    }

    /**
     * Verify if a token is valid
     */
    public function verifyToken($token)
    {
        $reset = $this->where('token', $token)
                      ->where('expires_at >', date('Y-m-d H:i:s'))
                      ->first();

        return $reset;
    }

    /**
     * Delete a token after use
     */
    public function deleteToken($token)
    {
        $this->where('token', $token)->delete();
    }

    /**
     * Clean up expired tokens
     */
    public function cleanupExpiredTokens()
    {
        $this->where('expires_at <', date('Y-m-d H:i:s'))->delete();
    }
}
