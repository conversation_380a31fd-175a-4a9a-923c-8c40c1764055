<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-file-invoice"></i> Invoice #<?= $booking['id'] ?></h1>
    <div>
        <a href="<?= base_url('/user/bookings') ?>" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Bookings
        </a>
        <a href="<?= base_url('/user/invoice/print/' . $booking['id']) ?>" class="btn btn-primary" target="_blank">
            <i class="fas fa-print"></i> Print Invoice
        </a>
    </div>
</div>

<div class="glass-card">
    <div class="card-body">
        <!-- Invoice Header -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="text-primary mb-3">Tiffin Delight</h2>
                <p class="mb-1"><strong>Address:</strong> 123 Food Street, Taste City</p>
                <p class="mb-1"><strong>Phone:</strong> +91 98765 43210</p>
                <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                <p class="mb-0"><strong>GST No:</strong> 27AAAAA0000A1Z5</p>
            </div>
            <div class="col-md-6 text-md-end">
                <h3 class="text-primary">INVOICE</h3>
                <p class="mb-1"><strong>Invoice No:</strong> INV-<?= str_pad($booking['id'], 6, '0', STR_PAD_LEFT) ?></p>
                <p class="mb-1"><strong>Date:</strong> <?= date('F j, Y', strtotime($booking['created_at'])) ?></p>
                <p class="mb-1"><strong>Delivery Date:</strong> <?= date('F j, Y', strtotime($booking['booking_date'])) ?></p>
                <?php if ($slot): ?>
                    <p class="mb-0"><strong>Delivery Time:</strong> <?= $slot['slot_time'] ?></p>
                <?php endif; ?>
            </div>
        </div>

        <hr>

        <!-- Customer Information -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h5 class="text-primary mb-3">Bill To:</h5>
                <p class="mb-1"><strong><?= $user['name'] ?></strong></p>
                <p class="mb-1"><?= $user['email'] ?></p>
                <p class="mb-0"><?= $user['phone'] ?></p>
            </div>
            <div class="col-md-6">
                <h5 class="text-primary mb-3">Order Status:</h5>
                <p class="mb-1">
                    <strong>Status:</strong>
                    <?php
                    $statusClass = '';
                    $statusText = '';
                    switch ($booking['status']) {
                        case 'pending':
                            $statusClass = 'warning';
                            $statusText = 'Pending';
                            break;
                        case 'confirmed':
                            $statusClass = 'primary';
                            $statusText = 'Confirmed';
                            break;
                        case 'delivered':
                            $statusClass = 'success';
                            $statusText = 'Delivered';
                            break;
                        case 'cancelled':
                            $statusClass = 'danger';
                            $statusText = 'Cancelled';
                            break;
                    }
                    ?>
                    <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                </p>
                <p class="mb-0">
                    <strong>Payment Method:</strong>
                    <?php if ($booking['payment_method'] == 'wallet'): ?>
                        <span class="badge bg-success">Wallet</span>
                    <?php elseif ($booking['payment_method'] == 'razorpay'): ?>
                        <span class="badge bg-info">Online Payment</span>
                    <?php else: ?>
                        <span class="badge bg-secondary">Cash on Delivery</span>
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <hr>

        <!-- Order Items -->
        <div class="mb-4">
            <h5 class="text-primary mb-3">Order Details:</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Item</th>
                            <th class="text-center">Quantity</th>
                            <th class="text-end">Unit Price</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $subtotal = 0;
                        foreach ($items as $item):
                            $itemTotal = $item['price'] * $item['quantity'];
                            $subtotal += $itemTotal;
                        ?>
                            <tr>
                                <td>
                                    <strong><?= $item['dish_name'] ?></strong>
                                </td>
                                <td class="text-center"><?= $item['quantity'] ?></td>
                                <td class="text-end">₹<?= number_format($item['price'], 2) ?></td>
                                <td class="text-end">₹<?= number_format($itemTotal, 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th colspan="3" class="text-end">Subtotal:</th>
                            <th class="text-end">₹<?= number_format($subtotal, 2) ?></th>
                        </tr>
                        <tr>
                            <th colspan="3" class="text-end">Tax (0%):</th>
                            <th class="text-end">₹0.00</th>
                        </tr>
                        <tr>
                            <th colspan="3" class="text-end">Total Amount:</th>
                            <th class="text-end">₹<?= number_format($booking['total_amount'], 2) ?></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <hr>

        <!-- Footer -->
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">Terms & Conditions:</h6>
                <ul class="small text-muted">
                    <li>All orders are subject to availability</li>
                    <li>Delivery charges may apply for certain areas</li>
                    <li>Cancellation allowed up to 2 hours before delivery</li>
                    <li>For any queries, contact customer support</li>
                </ul>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-1"><strong>Thank you for choosing Tiffin Delight!</strong></p>
                <p class="small text-muted">This is a computer-generated invoice.</p>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
