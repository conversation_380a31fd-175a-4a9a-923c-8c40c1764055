# Disable directory browsing
Options -Indexes

# ----------------------------------------------------------------------
# Rewrite engine
# ----------------------------------------------------------------------

# Turning on the rewrite engine is necessary for the following rules and features.
# FollowSymLinks must be enabled for this to work.
<IfModule mod_rewrite.c>
	Options +FollowSymlinks
	RewriteEngine On

	# If you installed CodeIgniter in a subfolder, you will need to
	# change the following line to match the subfolder you need.
	# http://httpd.apache.org/docs/current/mod/mod_rewrite.html#rewritebase
	# RewriteBase /

	# Redirect Trailing Slashes...
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteCond %{REQUEST_URI} (.+)/$
	RewriteRule ^ %1 [L,R=301]

	# Rewrite "www.example.com -> example.com"
	RewriteCond %{HTTPS} !=on
	RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
	RewriteRule ^ http://%1%{REQUEST_URI} [R=301,L]

	# Checks to see if the user is attempting to access a valid file,
	# such as an image or css document, if this isn't true it sends the
	# request to the front controller, index.php
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule ^(.*)$ index.php/$1 [L]

	# Ensure Authorization header is passed along
	RewriteCond %{HTTP:Authorization} .
	RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
</IfModule>

<IfModule !mod_rewrite.c>
	# If we don't have mod_rewrite installed, all 404's
	# can be sent to index.php, and everything works as normal.
	ErrorDocument 404 index.php
</IfModule>

# ----------------------------------------------------------------------
# CORS Settings for API
# ----------------------------------------------------------------------
<IfModule mod_headers.c>
    # Set CORS headers for all API routes
    SetEnvIf Origin "http(s)?://(localhost(:[0-9]+)?|127\.0\.0\.1(:[0-9]+)?)" AccessControlAllowOrigin=$0
    Header always set Access-Control-Allow-Origin %{AccessControlAllowOrigin}e env=AccessControlAllowOrigin
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    Header always set Access-Control-Max-Age "1000"
    Header always set Access-Control-Allow-Credentials "true"

    # Handle preflight OPTIONS requests
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# Disable server signature start
ServerSignature Off
# Disable server signature end
