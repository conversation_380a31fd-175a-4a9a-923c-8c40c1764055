<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<!-- User Bookings Header -->
<div class="container-fluid bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-dark mb-3">
                    <i class="fas fa-list-alt text-primary me-3"></i>My Bookings
                </h1>
                <p class="lead text-muted mb-4">View and manage all your tiffin orders</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('/') ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('/user/dashboard') ?>" class="text-decoration-none">Dashboard</a></li>
                        <li class="breadcrumb-item active">My Bookings</li>
                    </ol>
                </nav>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex align-items-center justify-content-lg-end">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 80px; height: 80px;">
                        <span class="fw-bold" style="font-size: 2rem;"><?= strtoupper(substr(session()->get('name'), 0, 1)) ?></span>
                    </div>
                    <div class="text-start">
                        <h4 class="mb-1 fw-bold text-dark"><?= session()->get('name') ?></h4>
                        <p class="mb-0 text-muted"><?= session()->get('email') ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Bookings Content -->
<div class="container py-5">
    <!-- User Navigation Menu -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/dashboard') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                                <span class="fw-bold">Dashboard</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/profile') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-user-edit fa-2x mb-2"></i>
                                <span class="fw-bold">Profile</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/bookings') ?>" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-list-alt fa-2x mb-2"></i>
                                <span class="fw-bold">My Bookings</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/wallet') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-wallet fa-2x mb-2"></i>
                                <span class="fw-bold">My Wallet</span>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-8 col-12">
                            <a href="<?= base_url('/booking/create') ?>" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span class="fw-bold">Book New Tiffin</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (empty($bookings)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> You don't have any bookings yet.
            <a href="<?= base_url('/booking/create') ?>" class="alert-link">Book your first tiffin now!</a>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-body">
                <ul class="nav nav-tabs mb-3" id="bookingTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">All</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="false">Pending</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="confirmed-tab" data-bs-toggle="tab" data-bs-target="#confirmed" type="button" role="tab" aria-controls="confirmed" aria-selected="false">Confirmed</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="delivered-tab" data-bs-toggle="tab" data-bs-target="#delivered" type="button" role="tab" aria-controls="delivered" aria-selected="false">Delivered</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled" type="button" role="tab" aria-controls="cancelled" aria-selected="false">Cancelled</button>
                    </li>
                </ul>

                <div class="tab-content" id="bookingTabsContent">
                    <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                        <?= view('user/_booking_table', ['bookings' => $bookings]) ?>
                    </div>
                    <div class="tab-pane fade" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                        <?php
                        $pendingBookings = array_filter($bookings, function ($booking) {
                            return $booking['status'] == 'pending';
                        });
                        ?>
                        <?= view('user/_booking_table', ['bookings' => $pendingBookings]) ?>
                    </div>
                    <div class="tab-pane fade" id="confirmed" role="tabpanel" aria-labelledby="confirmed-tab">
                        <?php
                        $confirmedBookings = array_filter($bookings, function ($booking) {
                            return $booking['status'] == 'confirmed';
                        });
                        ?>
                        <?= view('user/_booking_table', ['bookings' => $confirmedBookings]) ?>
                    </div>
                    <div class="tab-pane fade" id="delivered" role="tabpanel" aria-labelledby="delivered-tab">
                        <?php
                        $deliveredBookings = array_filter($bookings, function ($booking) {
                            return $booking['status'] == 'delivered';
                        });
                        ?>
                        <?= view('user/_booking_table', ['bookings' => $deliveredBookings]) ?>
                    </div>
                    <div class="tab-pane fade" id="cancelled" role="tabpanel" aria-labelledby="cancelled-tab">
                        <?php
                        $cancelledBookings = array_filter($bookings, function ($booking) {
                            return $booking['status'] == 'cancelled';
                        });
                        ?>
                        <?= view('user/_booking_table', ['bookings' => $cancelledBookings]) ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <style>
        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
        }
    </style>
    <?= $this->endSection() ?>