<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<h1 class="mb-4"><i class="fas fa-calendar-alt"></i> Manage Bookings</h1>

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= base_url('/admin/bookings') ?>" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search"
                    value="<?= esc($search ?? '') ?>" placeholder="Booking ID or Amount...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" <?= ($status ?? 'all') === 'all' ? 'selected' : '' ?>>All Status</option>
                    <option value="pending" <?= ($status ?? '') === 'pending' ? 'selected' : '' ?>>Pending (<?= $pendingCount ?>)</option>
                    <option value="confirmed" <?= ($status ?? '') === 'confirmed' ? 'selected' : '' ?>>Confirmed (<?= $confirmedCount ?>)</option>
                    <option value="delivered" <?= ($status ?? '') === 'delivered' ? 'selected' : '' ?>>Delivered (<?= $deliveredCount ?>)</option>
                    <option value="cancelled" <?= ($status ?? '') === 'cancelled' ? 'selected' : '' ?>>Cancelled (<?= $cancelledCount ?>)</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from"
                    value="<?= esc($dateFrom ?? '') ?>">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to"
                    value="<?= esc($dateTo ?? '') ?>">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="<?= base_url('/admin/bookings') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Clear Filters
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results Summary -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <span class="text-muted">
            Showing <?= count($bookings) ?> bookings
            <?php if ($search || $status !== 'all' || $dateFrom || $dateTo): ?>
                (filtered from <?= $totalBookings ?> total)
            <?php endif; ?>
        </span>
    </div>
    <div>
        <span class="text-muted">Page <?= $currentPage ?></span>
    </div>
</div>

<?php if (empty($bookings)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> No bookings found with the current filters.
    </div>
<?php else: ?>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Payment</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($bookings as $booking): ?>
                            <tr>
                                <td>#<?= $booking['id'] ?></td>
                                <td>
                                    <?php
                                    // Get user name from database
                                    $db = \Config\Database::connect();
                                    $user = $db->table('users')->where('id', $booking['user_id'])->get()->getRowArray();
                                    echo $user ? $user['name'] : 'Unknown User';
                                    ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($booking['booking_date'])) ?></td>
                                <td>₹<?= number_format($booking['total_amount'], 2) ?></td>
                                <td>
                                    <?php
                                    $paymentClass = '';
                                    switch ($booking['payment_method']) {
                                        case 'wallet':
                                            $paymentClass = 'success';
                                            break;
                                        case 'razorpay':
                                            $paymentClass = 'primary';
                                            break;
                                        case 'cod':
                                            $paymentClass = 'warning';
                                            break;
                                        default:
                                            $paymentClass = 'secondary';
                                    }
                                    ?>
                                    <span class="badge bg-<?= $paymentClass ?>">
                                        <?= ucfirst($booking['payment_method']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    switch ($booking['status']) {
                                        case 'pending':
                                            $statusClass = 'warning';
                                            break;
                                        case 'confirmed':
                                            $statusClass = 'info';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'success';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'danger';
                                            break;
                                        default:
                                            $statusClass = 'secondary';
                                    }
                                    ?>
                                    <span class="badge bg-<?= $statusClass ?>">
                                        <?= ucfirst($booking['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="<?= base_url('/admin/bookings/view/' . $booking['id']) ?>" class="btn btn-sm btn-info text-white">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($pager->getPageCount() > 1): ?>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="text-muted">
                            Showing <?= ($currentPage - 1) * $perPage + 1 ?> to
                            <?= ($currentPage - 1) * $perPage + count($bookings) ?> of
                            <?= $totalBookings ?> entries
                        </span>
                    </div>
                    <div>
                        <?= $pager->links('default', 'bootstrap_pagination') ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?= $this->endSection() ?>