<!-- Premium Navigation -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top premium">
    <div class="container">
        <a class="navbar-brand" href="<?= base_url('/') ?>">
            <?php if (site_logo()): ?>
                <img src="<?= site_logo() ?>" alt="<?= site_name() ?>" style="height: 40px;" class="me-2">
            <?php else: ?>
                <i class="fas fa-utensils me-2"></i>
            <?php endif; ?>
            <?= site_name() ?>
        </a>

        <!-- Mobile Cart Icon (Only visible on mobile) -->
        <?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
            <?php
            $cart = session()->get('cart');
            $guest_cart = session()->get('guest_cart');
            $totalItems = 0;

            // Calculate total items from both carts
            if (!empty($cart)) {
                $totalItems += array_sum(array_column($cart, 'quantity'));
            }
            if (!empty($guest_cart)) {
                $totalItems += array_sum(array_column($guest_cart, 'quantity'));
            }
            ?>
            <button class="mobile-cart-icon d-lg-none" onclick="toggleCartSidebar()">
                <i class="fas fa-shopping-cart"></i>
                <?php if ($totalItems > 0): ?>
                    <span class="cart-badge" id="mobile-cart-badge"><?= $totalItems ?></span>
                <?php endif; ?>
            </button>
        <?php else: ?>
            <?php
            $guest_cart = session()->get('guest_cart');
            $totalItems = 0;
            if (!empty($guest_cart)) {
                $totalItems = array_sum(array_column($guest_cart, 'quantity'));
            }
            ?>
            <?php if ($totalItems > 0): ?>
                <button class="mobile-cart-icon d-lg-none" onclick="toggleCartSidebar()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-badge" id="mobile-cart-badge"><?= $totalItems ?></span>
                </button>
            <?php endif; ?>
        <?php endif; ?>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mx-auto">
                <li class="nav-item">
                    <a class="nav-link <?= current_url() == base_url('/') ? 'active' : '' ?>" href="<?= base_url('/') ?>">
                        <i class="fas fa-home me-1"></i> Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= strpos(current_url(), '/dishes') !== false ? 'active' : '' ?>" href="<?= base_url('/dishes') ?>">
                        <i class="fas fa-utensils me-1"></i> Menu
                    </a>
                </li>
                <?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos(current_url(), '/booking/create') !== false ? 'active' : '' ?>" href="<?= base_url('/booking/create') ?>">
                            <i class="fas fa-shopping-cart me-1"></i> Book Tiffin
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos(current_url(), '/user/bookings') !== false ? 'active' : '' ?>" href="<?= base_url('/user/bookings') ?>">
                            <i class="fas fa-list-alt me-1"></i> My Orders
                        </a>
                    </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link" href="#about">
                        <i class="fas fa-info-circle me-1"></i> About
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#contact">
                        <i class="fas fa-phone me-1"></i> Contact
                    </a>
                </li>
            </ul>

            <!-- Mobile User Menu (Only visible on mobile) -->
            <?php if (session()->get('logged_in')): ?>
                <div class="mobile-user-menu d-lg-none">
                    <?php if (session()->get('is_admin')): ?>
                        <div class="mobile-user-header">
                            <i class="fas fa-user-circle fa-2x text-primary"></i>
                            <div class="ms-2">
                                <h6 class="mb-0"><?= session()->get('name') ?></h6>
                                <small class="text-muted">Administrator</small>
                            </div>
                        </div>
                        <div class="mobile-menu-links">
                            <a href="<?= base_url('/admin/dashboard') ?>">
                                <i class="fas fa-tachometer-alt me-2"></i> Admin Dashboard
                            </a>
                            <a href="<?= base_url('/admin/logout') ?>" class="text-danger">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="mobile-user-header">
                            <i class="fas fa-user-circle fa-2x text-primary"></i>
                            <div class="ms-2">
                                <h6 class="mb-0"><?= session()->get('name') ?></h6>
                                <small class="text-muted"><?= session()->get('email') ?></small>
                            </div>
                        </div>
                        <div class="mobile-menu-links">
                            <a href="<?= base_url('/user/dashboard') ?>">
                                <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                            </a>
                            <a href="<?= base_url('/user/profile') ?>">
                                <i class="fas fa-user-edit me-2"></i> Profile
                            </a>
                            <a href="<?= base_url('/user/wallet') ?>">
                                <i class="fas fa-wallet me-2"></i> Wallet
                            </a>
                            <a href="<?= base_url('/user/bookings') ?>">
                                <i class="fas fa-list-alt me-2"></i> My Bookings
                            </a>
                            <a href="<?= base_url('/auth/logout') ?>" class="text-danger">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="mobile-auth-buttons d-lg-none">
                    <a class="btn btn-success w-100 mb-2" href="<?= base_url('/auth/login') ?>">
                        <i class="fas fa-sign-in-alt me-1"></i> Login
                    </a>
                    <a class="btn btn-primary w-100" href="<?= base_url('/auth/register') ?>">
                        <i class="fas fa-user-plus me-1"></i> Register
                    </a>
                </div>
            <?php endif; ?>

            <!-- Desktop User Menu (Only visible on desktop) -->
            <div class="d-none d-lg-flex align-items-center">
                <?php if (session()->get('logged_in')): ?>
                    <?php if (session()->get('is_admin')): ?>
                        <!-- Admin User -->
                        <div class="dropdown">
                            <a class="btn btn-sm btn-outline-secondary dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-shield me-2"></i>
                                <span class="d-none d-md-inline"><?= session()->get('name') ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow border-0" aria-labelledby="navbarDropdown">
                                <li>
                                    <div class="dropdown-item text-center py-3">
                                        <div class="mb-2">
                                            <i class="fas fa-user-shield fa-3x text-primary"></i>
                                        </div>
                                        <h6 class="mb-0"><?= session()->get('name') ?></h6>
                                        <small class="text-muted">Administrator</small>
                                    </div>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="<?= base_url('/admin/dashboard') ?>"><i class="fas fa-tachometer-alt me-2"></i> Dashboard</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('/admin/dishes') ?>"><i class="fas fa-utensils me-2"></i> Manage Dishes</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('/admin/bookings') ?>"><i class="fas fa-list-alt me-2"></i> Manage Bookings</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('/admin/users') ?>"><i class="fas fa-users me-2"></i> Manage Users</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item text-danger" href="<?= base_url('/auth/logout') ?>"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <!-- Regular User -->
                        <?php
                        $cart = session()->get('cart');
                        $guest_cart = session()->get('guest_cart');
                        $totalItems = 0;

                        // Calculate total items from both carts
                        if (!empty($cart)) {
                            $totalItems += array_sum(array_column($cart, 'quantity'));
                        }
                        if (!empty($guest_cart)) {
                            $totalItems += array_sum(array_column($guest_cart, 'quantity'));
                        }
                        ?>
                        <?php if ($totalItems > 0): ?>
                            <button class="btn btn-sm btn-primary position-relative me-3" onclick="toggleCartSidebar()">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-accent text-dark" id="desktop-cart-badge">
                                    <?= $totalItems ?>
                                    <span class="visually-hidden">items in cart</span>
                                </span>
                            </button>
                        <?php endif; ?>
                        <div class="dropdown">
                            <a class="btn btn-sm btn-outline-secondary dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-2"></i>
                                <span class="d-none d-md-inline"><?= session()->get('name') ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow border-0" aria-labelledby="navbarDropdown">
                                <li>
                                    <div class="dropdown-item text-center py-3">
                                        <div class="mb-2">
                                            <i class="fas fa-user-circle fa-3x text-primary"></i>
                                        </div>
                                        <h6 class="mb-0"><?= session()->get('name') ?></h6>
                                        <small class="text-muted"><?= session()->get('email') ?></small>
                                    </div>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="<?= base_url('/user/dashboard') ?>"><i class="fas fa-tachometer-alt me-2"></i> Dashboard</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('/user/profile') ?>"><i class="fas fa-user-edit me-2"></i> Profile</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('/user/wallet') ?>"><i class="fas fa-wallet me-2"></i> Wallet</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('/user/bookings') ?>"><i class="fas fa-list-alt me-2"></i> My Bookings</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item text-danger" href="<?= base_url('/auth/logout') ?>"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                            </ul>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <!-- Guest User -->
                    <?php
                    $guest_cart = session()->get('guest_cart');
                    $totalItems = 0;
                    if (!empty($guest_cart)) {
                        $totalItems = array_sum(array_column($guest_cart, 'quantity'));
                    }
                    ?>
                    <?php if ($totalItems > 0): ?>
                        <button class="btn btn-sm btn-primary position-relative me-3" onclick="toggleCartSidebar()">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-accent text-dark" id="desktop-cart-badge">
                                <?= $totalItems ?>
                                <span class="visually-hidden">items in cart</span>
                            </span>
                        </button>
                    <?php endif; ?>
                    <a href="<?= base_url('/auth/login') ?>" class="btn btn-sm btn-outline-primary me-2">
                        <i class="fas fa-sign-in-alt me-1"></i> Login
                    </a>
                    <a href="<?= base_url('/auth/register') ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-user-plus me-1"></i> Register
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>