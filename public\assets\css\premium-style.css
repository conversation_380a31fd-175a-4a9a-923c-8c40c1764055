/* ===== BOOTSTRAP 5 PREMIUM TIFFIN SERVICE ===== */

/* Minimal custom styles to enhance Bootstrap 5 */
:root {
    --bs-success: #198754;
    --bs-primary: #0d6efd;
}

/* Custom object-fit utility for older browsers */
.object-fit-cover {
    object-fit: cover;
}

/* Smooth transitions for all interactive elements */
.btn, .card, .carousel-item {
    transition: all 0.3s ease;
}

/* Enhanced card hover effects */
.card:hover {
    transform: translateY(-5px);
}

/* Custom carousel height */
.carousel-item {
    min-height: 350px;
}

/* Ensure equal height cards */
.h-100 {
    height: 100% !important;
}

/* Custom badge styles */
.badge {
    font-size: 0.75em;
}

/* Responsive image improvements */
img {
    max-width: 100%;
    height: auto;
}

/* Custom spacing utilities */
.py-6 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
}

/* Enhanced shadow utilities */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .lead {
        font-size: 1.1rem;
    }
}

.hero-slide {
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    position: relative;
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.6;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 2rem;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Swiper Customization */
.banner-slider .swiper-pagination-bullet {
    background: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

.banner-slider .swiper-pagination-bullet-active {
    background: #fff;
}

.banner-slider .swiper-button-next,
.banner-slider .swiper-button-prev {
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

/* Features Section */
.features-section {
    position: relative;
    z-index: 10;
    margin-top: -100px;
    padding-top: 120px;
}

.feature-card {
    background: #fff;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(76, 175, 80, 0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--premium-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #fff;
    margin-bottom: 1.5rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.section-divider {
    width: 80px;
    height: 4px;
    background: var(--premium-primary);
    border-radius: 2px;
    margin: 0 auto 3rem;
}

/* Premium Full-Width Dishes Section */
.premium-dishes-section {
    position: relative;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    overflow: hidden;
}

.dishes-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
    opacity: 0.03;
    z-index: 0;
}

.section-header {
    position: relative;
    z-index: 1;
}

.premium-section-title {
    font-size: 3rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 1rem;
    letter-spacing: -0.02em;
}

.premium-section-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
    font-weight: 400;
}

.premium-divider {
    width: 100px;
    height: 4px;
    background: var(--premium-primary);
    border-radius: 2px;
    margin: 0 auto 3rem;
}

/* Premium Product Grid */
.premium-product-grid {
    position: relative;
    z-index: 1;
    padding: 2rem 0 4rem;
}

.products-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 0 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.premium-product-card {
    background: #fff;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.premium-product-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
}

.product-card-inner {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Product Image */
.product-image-container {
    position: relative;
    height: 280px;
    overflow: hidden;
    border-radius: 24px 24px 0 0;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.premium-product-card:hover .product-image {
    transform: scale(1.1);
}

/* Product Overlay */
.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(46, 125, 50, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.premium-product-card:hover .product-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    transform: translateY(20px);
    transition: transform 0.4s ease;
}

.premium-product-card:hover .overlay-content {
    transform: translateY(0);
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 160px;
}

.primary-action {
    background: #fff;
    color: #4CAF50;
    border: 2px solid #fff;
}

.primary-action:hover {
    background: transparent;
    color: #fff;
    transform: translateY(-2px);
}

.secondary-action {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.secondary-action:hover {
    background: #fff;
    color: #4CAF50;
    transform: translateY(-2px);
}

/* Product Badges */
.product-badges {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    z-index: 2;
}

.status-badge {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status-badge.available {
    color: #28a745;
}

.status-badge.sold-out {
    color: #dc3545;
}

.diet-badge {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.diet-badge.veg {
    color: #28a745;
}

.diet-badge.non-veg {
    color: #dc3545;
}

/* Product Content */
.product-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.product-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    flex: 1;
    line-height: 1.3;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 800;
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    padding: 8px 16px;
    border-radius: 12px;
    white-space: nowrap;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 1rem;
}

.product-rating i {
    color: #ffc107;
    font-size: 1rem;
}

.rating-count {
    font-size: 0.9rem;
    color: #6c757d;
    margin-left: 8px;
}

.product-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.product-footer {
    margin-top: auto;
}

.btn-add-cart {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 14px 24px;
    background: var(--premium-primary);
    color: #fff;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-add-cart:hover {
    background: var(--premium-secondary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
    color: #fff;
}

.btn-sold-out {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 14px 24px;
    background: #6c757d;
    color: #fff;
    border-radius: 12px;
    font-weight: 600;
    border: none;
    cursor: not-allowed;
    opacity: 0.7;
}

/* View All Section */
.view-all-section {
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(46, 125, 50, 0.05) 100%);
}

.btn-view-all {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 18px 40px;
    background: var(--premium-primary);
    color: #fff;
    text-decoration: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
}

.btn-view-all:hover {
    background: var(--premium-secondary);
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(76, 175, 80, 0.4);
    color: #fff;
}

.btn-view-all i {
    transition: transform 0.3s ease;
}

.btn-view-all:hover i {
    transform: translateX(5px);
}

.view-all-subtitle {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

/* Empty State */
.premium-empty-state {
    position: relative;
    z-index: 1;
    padding: 5rem 0;
}

.empty-content {
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.empty-icon {
    width: 120px;
    height: 120px;
    background: rgba(76, 175, 80, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
}

.empty-icon i {
    font-size: 3rem;
    color: #4CAF50;
}

.empty-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.empty-content p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

/* Reviews Section */
.reviews-section {
    padding: 5rem 0;
}

.review-card {
    background: #fff;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    background: var(--premium-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: 700;
    font-size: 1.2rem;
}

.reviewer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reviewer-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.review-date {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 2px;
}

.review-rating {
    display: flex;
    gap: 2px;
}

.review-rating i {
    color: #ffc107;
    font-size: 1rem;
}

.review-content p {
    color: #495057;
    line-height: 1.6;
    margin: 0;
    font-style: italic;
}

/* Call to Action Section */
.cta-section {
    background: var(--premium-primary);
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1505253758473-96b7015fcd40?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
    opacity: 0.1;
    z-index: 0;
}

.cta-section .container {
    position: relative;
    z-index: 1;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #fff;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .products-wrapper {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
        padding: 0 1.5rem;
    }

    .premium-section-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-section {
        margin-top: -50px;
        padding-top: 70px;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .products-wrapper {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .premium-section-title {
        font-size: 2.2rem;
    }

    .product-actions {
        flex-direction: row;
        gap: 0.5rem;
    }

    .action-btn {
        min-width: 120px;
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .btn-premium {
        padding: 10px 25px;
        font-size: 0.9rem;
    }

    .premium-section-title {
        font-size: 2rem;
    }

    .premium-section-subtitle {
        font-size: 1.1rem;
    }

    .products-wrapper {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .product-image-container {
        height: 240px;
    }

    .product-content {
        padding: 1.5rem;
    }

    .product-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .product-title {
        font-size: 1.2rem;
    }

    .product-price {
        font-size: 1.3rem;
        align-self: flex-end;
    }

    .product-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .action-btn {
        min-width: auto;
        width: 100%;
    }

    .btn-view-all {
        padding: 16px 32px;
        font-size: 1rem;
    }

    .product-badges {
        top: 15px;
        left: 15px;
        right: 15px;
    }

    .status-badge {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .diet-badge {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .premium-section-title {
        font-size: 1.8rem;
    }

    .products-wrapper {
        padding: 0 0.5rem;
    }

    .premium-product-card {
        border-radius: 16px;
    }

    .product-image-container {
        height: 200px;
        border-radius: 16px 16px 0 0;
    }

    .product-content {
        padding: 1.2rem;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .product-price {
        font-size: 1.2rem;
        padding: 6px 12px;
    }

    .btn-add-cart,
    .btn-sold-out {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .empty-icon {
        width: 80px;
        height: 80px;
    }

    .empty-icon i {
        font-size: 2rem;
    }

    .empty-content h3 {
        font-size: 1.5rem;
    }
}
