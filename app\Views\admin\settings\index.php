<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cog me-2"></i>Global Settings
        </h1>
    </div>

    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Website Settings -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-globe me-2"></i>Website Settings
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('/admin/settings') ?>" method="POST" enctype="multipart/form-data">
                        <?= csrf_field() ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">Website Name</label>
                                    <input type="text" class="form-control" id="site_name" name="site_name"
                                        value="<?= $settings['site_name'] ?? 'Tiffin Delight' ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                    <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                        value="<?= $settings['currency_symbol'] ?? '₹' ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_code" class="form-label">Currency Code</label>
                                    <input type="text" class="form-control" id="currency_code" name="currency_code"
                                        value="<?= $settings['currency_code'] ?? 'INR' ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="google_analytics" class="form-label">Google Analytics ID</label>
                                    <input type="text" class="form-control" id="google_analytics" name="google_analytics"
                                        value="<?= $settings['google_analytics'] ?? '' ?>" placeholder="G-XXXXXXXXXX">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_logo" class="form-label">Website Logo</label>
                                    <input type="file" class="form-control" id="site_logo" name="site_logo" accept="image/*">
                                    <?php if (!empty($settings['site_logo'])): ?>
                                        <div class="mt-2">
                                            <img src="<?= base_url('/uploads/settings/' . $settings['site_logo']) ?>"
                                                alt="Current Logo" class="img-thumbnail" style="max-height: 100px;">
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_favicon" class="form-label">Website Favicon</label>
                                    <input type="file" class="form-control" id="site_favicon" name="site_favicon" accept="image/*">
                                    <?php if (!empty($settings['site_favicon'])): ?>
                                        <div class="mt-2">
                                            <img src="<?= base_url('/uploads/settings/' . $settings['site_favicon']) ?>"
                                                alt="Current Favicon" class="img-thumbnail" style="max-height: 50px;">
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">Contact Information</h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Phone Number</label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone"
                                        value="<?= $settings['contact_phone'] ?? '' ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email"
                                        value="<?= $settings['contact_email'] ?? '' ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="contact_address" class="form-label">Address</label>
                            <textarea class="form-control" id="contact_address" name="contact_address" rows="3" required><?= $settings['contact_address'] ?? '' ?></textarea>
                        </div>

                        <h5 class="mt-4 mb-3">Social Media Links</h5>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="facebook_url" class="form-label">
                                        <i class="fab fa-facebook-f me-2"></i>Facebook URL
                                    </label>
                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                        value="<?= $settings['facebook_url'] ?? '' ?>" placeholder="https://facebook.com/yourpage">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="twitter_url" class="form-label">
                                        <i class="fab fa-twitter me-2"></i>Twitter URL
                                    </label>
                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                        value="<?= $settings['twitter_url'] ?? '' ?>" placeholder="https://twitter.com/yourhandle">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="instagram_url" class="form-label">
                                        <i class="fab fa-instagram me-2"></i>Instagram URL
                                    </label>
                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                        value="<?= $settings['instagram_url'] ?? '' ?>" placeholder="https://instagram.com/yourhandle">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="youtube_url" class="form-label">
                                        <i class="fab fa-youtube me-2"></i>YouTube URL
                                    </label>
                                    <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                        value="<?= $settings['youtube_url'] ?? '' ?>" placeholder="https://youtube.com/yourchannel">
                                </div>
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">Business Hours</h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_hours_weekday" class="form-label">Weekday Hours (Mon-Sat)</label>
                                    <input type="text" class="form-control" id="business_hours_weekday" name="business_hours_weekday"
                                        value="<?= $settings['business_hours_weekday'] ?? '8:00 AM - 10:00 PM' ?>" placeholder="8:00 AM - 10:00 PM">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_hours_sunday" class="form-label">Sunday Hours</label>
                                    <input type="text" class="form-control" id="business_hours_sunday" name="business_hours_sunday"
                                        value="<?= $settings['business_hours_sunday'] ?? '9:00 AM - 9:00 PM' ?>" placeholder="9:00 AM - 9:00 PM">
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Admin Password Change -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-key me-2"></i>Change Admin Password
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('/admin/settings/change-password') ?>" method="POST">
                        <?= csrf_field() ?>

                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-lock me-2"></i>Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Settings Info
                    </h6>
                </div>
                <div class="card-body">
                    <div class="small text-muted">
                        <p><strong>Logo:</strong> Recommended size 200x60px</p>
                        <p><strong>Favicon:</strong> Recommended size 32x32px or 16x16px</p>
                        <p><strong>Images:</strong> Supported formats: JPG, PNG, GIF</p>
                        <p><strong>Max file size:</strong> 2MB per image</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>