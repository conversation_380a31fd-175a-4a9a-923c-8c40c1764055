<!-- Footer -->
<footer id="contact" class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <?php
            // Get settings with direct database query
            $siteName = 'BoxBites';
            $logoUrl = null;
            $contactAddress = '123 Food Street, Delicious City, FC 12345';
            $contactPhone = '+91 9876543210';
            $contactEmail = '<EMAIL>';
            $businessHoursWeekday = '8:00 AM - 10:00 PM';
            $businessHoursSunday = '9:00 AM - 9:00 PM';
            $facebookUrl = $instagramUrl = $twitterUrl = $youtubeUrl = null;

            try {
                $db = \Config\Database::connect();
                $query = $db->query("SELECT setting_key, setting_value FROM settings");
                $results = $query->getResultArray();

                foreach ($results as $row) {
                    if (!empty($row['setting_value'])) {
                        switch ($row['setting_key']) {
                            case 'site_name':
                                $siteName = $row['setting_value'];
                                break;
                            case 'site_logo':
                                $logoUrl = base_url() . 'uploads/settings/' . $row['setting_value'];
                                break;
                            case 'contact_address':
                                $contactAddress = $row['setting_value'];
                                break;
                            case 'contact_phone':
                                $contactPhone = $row['setting_value'];
                                break;
                            case 'contact_email':
                                $contactEmail = $row['setting_value'];
                                break;
                            case 'business_hours_weekday':
                                $businessHoursWeekday = $row['setting_value'];
                                break;
                            case 'business_hours_sunday':
                                $businessHoursSunday = $row['setting_value'];
                                break;
                            case 'facebook_url':
                                $facebookUrl = $row['setting_value'];
                                break;
                            case 'instagram_url':
                                $instagramUrl = $row['setting_value'];
                                break;
                            case 'twitter_url':
                                $twitterUrl = $row['setting_value'];
                                break;
                            case 'youtube_url':
                                $youtubeUrl = $row['setting_value'];
                                break;
                        }
                    }
                }
            } catch (Exception $e) {
                // Use defaults
            }
            ?>

            <!-- Column 1: About Us -->
            <div class="col-lg-4 col-md-6 mb-4 mb-lg-0">
                <div class="pe-lg-4">
                    <!-- Brand Logo/Name -->
                    <div class="mb-4">
                        <?php if ($logoUrl): ?>
                            <img src="<?= $logoUrl ?>" alt="<?= $siteName ?>" style="height: 45px;" onerror="this.style.display='none';">
                        <?php else: ?>
                            <h4 class="text-primary mb-0">
                                <i class="fas fa-utensils me-2"></i><?= $siteName ?>
                            </h4>
                        <?php endif; ?>
                    </div>

                    <!-- Description -->
                    <p class="text-muted mb-4">
                        Delicious homemade food delivered to your doorstep. We use fresh ingredients and authentic recipes to bring you the best tiffin experience.
                    </p>

                    <!-- Social Media Links -->
                    <?php if ($facebookUrl || $instagramUrl || $twitterUrl || $youtubeUrl): ?>
                        <div class="social-links">
                            <h6 class="text-primary mb-3">Follow Us</h6>
                            <div class="d-flex gap-2">
                                <?php if ($facebookUrl): ?>
                                    <a href="<?= $facebookUrl ?>" target="_blank" class="text-primary fs-4 me-3" aria-label="Facebook">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if ($instagramUrl): ?>
                                    <a href="<?= $instagramUrl ?>" target="_blank" class="text-primary fs-4 me-3" aria-label="Instagram">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if ($twitterUrl): ?>
                                    <a href="<?= $twitterUrl ?>" target="_blank" class="text-primary fs-4 me-3" aria-label="Twitter">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if ($youtubeUrl): ?>
                                    <a href="<?= $youtubeUrl ?>" target="_blank" class="text-primary fs-4" aria-label="YouTube">
                                        <i class="fab fa-youtube"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Column 2: Quick Links -->
            <div class="col-lg-4 col-md-6 mb-4 mb-lg-0">
                <div class="px-lg-4">
                    <h5 class="text-primary mb-4">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <a href="<?= base_url('/') ?>" class="text-muted text-decoration-none">
                                <i class="fas fa-home me-2 text-primary"></i>Home
                            </a>
                        </li>
                        <li class="mb-3">
                            <a href="<?= base_url('/dishes') ?>" class="text-muted text-decoration-none">
                                <i class="fas fa-utensils me-2 text-primary"></i>Our Menu
                            </a>
                        </li>
                        <?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
                            <li class="mb-3">
                                <a href="<?= base_url('/booking/create') ?>" class="text-muted text-decoration-none">
                                    <i class="fas fa-calendar-plus me-2 text-primary"></i>Book Tiffin
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="mb-3">
                            <a href="#about" class="text-muted text-decoration-none">
                                <i class="fas fa-info-circle me-2 text-primary"></i>About Us
                            </a>
                        </li>
                        <li class="mb-3">
                            <a href="#contact" class="text-muted text-decoration-none">
                                <i class="fas fa-envelope me-2 text-primary"></i>Contact
                            </a>
                        </li>
                        <li class="mb-3">
                            <a href="#" class="text-muted text-decoration-none">
                                <i class="fas fa-question-circle me-2 text-primary"></i>FAQ
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Column 3: Contact Information -->
            <div class="col-lg-4 col-md-12">
                <div class="ps-lg-4">
                    <h5 class="text-primary mb-4">Contact Information</h5>

                    <!-- Address -->
                    <div class="d-flex mb-3">
                        <div class="me-3">
                            <i class="fas fa-map-marker-alt text-primary"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-1">Address</h6>
                            <p class="text-muted mb-0"><?= nl2br($contactAddress) ?></p>
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="d-flex mb-3">
                        <div class="me-3">
                            <i class="fas fa-phone text-primary"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-1">Phone</h6>
                            <a href="tel:<?= str_replace([' ', '-', '(', ')'], '', $contactPhone) ?>" class="text-muted text-decoration-none">
                                <?= $contactPhone ?>
                            </a>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="d-flex mb-3">
                        <div class="me-3">
                            <i class="fas fa-envelope text-primary"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-1">Email</h6>
                            <a href="mailto:<?= $contactEmail ?>" class="text-muted text-decoration-none">
                                <?= $contactEmail ?>
                            </a>
                        </div>
                    </div>

                    <!-- Business Hours -->
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-clock text-primary"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-1">Business Hours</h6>
                            <p class="text-muted mb-0">
                                Mon-Sat: <?= $businessHoursWeekday ?><br>
                                Sunday: <?= $businessHoursSunday ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <hr class="my-4 border-secondary">
        <div class="row align-items-center">
            <div class="col-md-6 text-center text-md-start">
                <p class="mb-0 text-muted">&copy; <?= date('Y') ?> <?= $siteName ?>. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-center text-md-end">
                <p class="mb-0 text-muted">
                    Made with <i class="fas fa-heart text-danger"></i> for food lovers
                </p>
            </div>
        </div>
    </div>
</footer>