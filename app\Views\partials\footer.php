<!-- Footer -->
<footer id="contact">
    <div class="container">
        <div class="row py-5">
            <div class="col-lg-4 col-md-6 mb-5 mb-lg-0">
                <?php
                // Get settings directly
                try {
                    $settingModel = new \App\Models\SettingModel();
                    $siteName = $settingModel->getSetting('site_name', 'Tiffin Delight');
                    $siteLogo = $settingModel->getSetting('site_logo');
                    $logoUrl = $siteLogo ? base_url('/uploads/settings/' . $siteLogo) : null;
                } catch (Exception $e) {
                    $siteName = 'Tiffin Delight';
                    $logoUrl = null;
                }
                ?>
                <div class="mb-4">
                    <a href="<?= base_url('/') ?>" class="d-inline-block">
                        <?php if ($logoUrl): ?>
                            <img src="<?= $logoUrl ?>" alt="<?= $siteName ?>" style="height: 50px;" class="mb-2">
                        <?php else: ?>
                            <h5><i class="fas fa-utensils me-2"></i><?= $siteName ?></h5>
                        <?php endif; ?>
                    </a>
                </div>
                <p class="mb-4">Delicious homemade food delivered to your doorstep. We use fresh ingredients and authentic recipes to bring you the best tiffin experience.</p>
                <div class="social-links mb-4">
                    <?php
                    // Get social media settings
                    try {
                        $facebookUrl = $settingModel->getSetting('facebook_url');
                        $instagramUrl = $settingModel->getSetting('instagram_url');
                        $twitterUrl = $settingModel->getSetting('twitter_url');
                        $youtubeUrl = $settingModel->getSetting('youtube_url');
                    } catch (Exception $e) {
                        $facebookUrl = $instagramUrl = $twitterUrl = $youtubeUrl = null;
                    }
                    ?>
                    <?php if ($facebookUrl): ?>
                        <a href="<?= $facebookUrl ?>" target="_blank" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                    <?php endif; ?>
                    <?php if ($instagramUrl): ?>
                        <a href="<?= $instagramUrl ?>" target="_blank" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <?php endif; ?>
                    <?php if ($twitterUrl): ?>
                        <a href="<?= $twitterUrl ?>" target="_blank" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    <?php endif; ?>
                    <?php if ($youtubeUrl): ?>
                        <a href="<?= $youtubeUrl ?>" target="_blank" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    <?php endif; ?>
                </div>
                <div class="newsletter">
                    <h6 class="mb-3">Subscribe to our newsletter</h6>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Your email address" aria-label="Your email address">
                        <button class="btn btn-primary" type="button">Subscribe</button>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-6 mb-4 mb-lg-0">
                <h5>Quick Links</h5>
                <ul class="list-unstyled">
                    <li><a href="<?= base_url('/') ?>"><i class="fas fa-angle-right me-2"></i>Home</a></li>
                    <li><a href="<?= base_url('/dishes') ?>"><i class="fas fa-angle-right me-2"></i>Menu</a></li>
                    <?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
                        <li><a href="<?= base_url('/booking/create') ?>"><i class="fas fa-angle-right me-2"></i>Book Tiffin</a></li>
                    <?php endif; ?>
                    <li><a href="#about"><i class="fas fa-angle-right me-2"></i>About Us</a></li>
                    <li><a href="#contact"><i class="fas fa-angle-right me-2"></i>Contact</a></li>
                    <li><a href="#"><i class="fas fa-angle-right me-2"></i>FAQ</a></li>
                    <li><a href="#"><i class="fas fa-angle-right me-2"></i>Privacy Policy</a></li>
                    <li><a href="#"><i class="fas fa-angle-right me-2"></i>Terms of Service</a></li>
                </ul>
            </div>

            <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
                <h5>Contact Info</h5>
                <?php
                // Get contact settings
                try {
                    $contactAddress = $settingModel->getSetting('contact_address', '123 Food Street, Delicious City, FC 12345');
                    $contactPhone = $settingModel->getSetting('contact_phone', '+91 9876543210');
                    $contactEmail = $settingModel->getSetting('contact_email', '<EMAIL>');
                    $businessHoursWeekday = $settingModel->getSetting('business_hours_weekday', '8:00 AM - 10:00 PM');
                    $businessHoursSunday = $settingModel->getSetting('business_hours_sunday', '9:00 AM - 9:00 PM');
                } catch (Exception $e) {
                    $contactAddress = '123 Food Street, Delicious City, FC 12345';
                    $contactPhone = '+91 9876543210';
                    $contactEmail = '<EMAIL>';
                    $businessHoursWeekday = '8:00 AM - 10:00 PM';
                    $businessHoursSunday = '9:00 AM - 9:00 PM';
                }
                ?>
                <ul class="list-unstyled contact-info">
                    <li class="mb-3">
                        <i class="fas fa-map-marker-alt me-3"></i>
                        <div>
                            <strong>Address:</strong><br>
                            <?= nl2br($contactAddress) ?>
                        </div>
                    </li>
                    <li class="mb-3">
                        <i class="fas fa-phone me-3"></i>
                        <div>
                            <strong>Phone:</strong><br>
                            <a href="tel:<?= str_replace([' ', '-', '(', ')'], '', $contactPhone) ?>"><?= $contactPhone ?></a>
                        </div>
                    </li>
                    <li class="mb-3">
                        <i class="fas fa-envelope me-3"></i>
                        <div>
                            <strong>Email:</strong><br>
                            <a href="mailto:<?= $contactEmail ?>"><?= $contactEmail ?></a>
                        </div>
                    </li>
                    <li class="mb-3">
                        <i class="fas fa-clock me-3"></i>
                        <div>
                            <strong>Hours:</strong><br>
                            Mon-Sat: <?= $businessHoursWeekday ?><br>
                            Sunday: <?= $businessHoursSunday ?>
                        </div>
                    </li>
                </ul>
            </div>

            <div class="col-lg-3 col-md-6">
                <h5>Our Services</h5>
                <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-check me-2 text-primary"></i>Fresh Daily Meals</li>
                    <li class="mb-2"><i class="fas fa-check me-2 text-primary"></i>Home Delivery</li>
                    <li class="mb-2"><i class="fas fa-check me-2 text-primary"></i>Custom Meal Plans</li>
                    <li class="mb-2"><i class="fas fa-check me-2 text-primary"></i>Healthy Options</li>
                    <li class="mb-2"><i class="fas fa-check me-2 text-primary"></i>Vegetarian & Vegan</li>
                    <li class="mb-2"><i class="fas fa-check me-2 text-primary"></i>Corporate Catering</li>
                </ul>

                <div class="mt-4">
                    <h6>We Accept</h6>
                    <div class="payment-methods">
                        <i class="fab fa-cc-visa me-2 fs-4"></i>
                        <i class="fab fa-cc-mastercard me-2 fs-4"></i>
                        <i class="fab fa-cc-paypal me-2 fs-4"></i>
                        <i class="fab fa-cc-apple-pay fs-4"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer-bottom py-4 mt-4 border-top border-secondary">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; <?= date('Y') ?> Tiffin Delight. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p class="mb-0">Designed with <i class="fas fa-heart text-danger"></i> for good food</p>
                </div>
            </div>
        </div>
    </div>
</footer>