<!-- Footer -->
<footer id="contact" class="bg-dark text-light">
    <div class="container">
        <div class="row py-5">
            <!-- Column 1: Brand & Social -->
            <div class="col-lg-4 col-md-6 mb-5 mb-lg-0">
                <?php
                // Get settings with direct database query
                $siteName = 'BoxBites';
                $logoUrl = null;

                try {
                    $db = \Config\Database::connect();
                    $query = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('site_name', 'site_logo')");
                    $results = $query->getResultArray();

                    foreach ($results as $row) {
                        if ($row['setting_key'] === 'site_name' && !empty($row['setting_value'])) {
                            $siteName = $row['setting_value'];
                        }
                        if ($row['setting_key'] === 'site_logo' && !empty($row['setting_value'])) {
                            $logoUrl = base_url() . 'uploads/settings/' . $row['setting_value'];
                        }
                    }
                } catch (Exception $e) {
                    // Use defaults
                }
                ?>

                <!-- Brand Logo/Name -->
                <div class="mb-4">
                    <a href="<?= base_url('/') ?>" class="d-inline-block text-decoration-none">
                        <?php if ($logoUrl): ?>
                            <img src="<?= $logoUrl ?>" alt="<?= $siteName ?>" style="height: 50px;" class="mb-2" onerror="this.style.display='none';">
                        <?php else: ?>
                            <h4 class="text-primary mb-0"><i class="fas fa-utensils me-2"></i><?= $siteName ?></h4>
                        <?php endif; ?>
                    </a>
                </div>

                <!-- Description -->
                <p class="mb-4 text-light-emphasis">Delicious homemade food delivered to your doorstep. We use fresh ingredients and authentic recipes to bring you the best tiffin experience.</p>

                <!-- Social Media Links -->
                <div class="social-links mb-4">
                    <h6 class="mb-3 text-primary">Follow Us</h6>
                    <?php
                    // Get social media settings
                    $facebookUrl = $instagramUrl = $twitterUrl = $youtubeUrl = null;

                    try {
                        $query = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('facebook_url', 'instagram_url', 'twitter_url', 'youtube_url')");
                        $socialResults = $query->getResultArray();

                        foreach ($socialResults as $row) {
                            if ($row['setting_key'] === 'facebook_url' && !empty($row['setting_value'])) {
                                $facebookUrl = $row['setting_value'];
                            }
                            if ($row['setting_key'] === 'instagram_url' && !empty($row['setting_value'])) {
                                $instagramUrl = $row['setting_value'];
                            }
                            if ($row['setting_key'] === 'twitter_url' && !empty($row['setting_value'])) {
                                $twitterUrl = $row['setting_value'];
                            }
                            if ($row['setting_key'] === 'youtube_url' && !empty($row['setting_value'])) {
                                $youtubeUrl = $row['setting_value'];
                            }
                        }
                    } catch (Exception $e) {
                        // Use defaults
                    }
                    ?>
                    <div class="d-flex gap-3">
                        <?php if ($facebookUrl): ?>
                            <a href="<?= $facebookUrl ?>" target="_blank" class="btn btn-outline-primary btn-sm rounded-circle" style="width: 40px; height: 40px;" aria-label="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($instagramUrl): ?>
                            <a href="<?= $instagramUrl ?>" target="_blank" class="btn btn-outline-primary btn-sm rounded-circle" style="width: 40px; height: 40px;" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($twitterUrl): ?>
                            <a href="<?= $twitterUrl ?>" target="_blank" class="btn btn-outline-primary btn-sm rounded-circle" style="width: 40px; height: 40px;" aria-label="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($youtubeUrl): ?>
                            <a href="<?= $youtubeUrl ?>" target="_blank" class="btn btn-outline-primary btn-sm rounded-circle" style="width: 40px; height: 40px;" aria-label="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Newsletter Subscription -->
                <div class="newsletter">
                    <h6 class="mb-3 text-primary">Stay Updated</h6>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Your email address" aria-label="Your email address">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-paper-plane me-1"></i>Subscribe
                        </button>
                    </div>
                </div>
            </div>

            <!-- Column 2: Quick Links -->
            <div class="col-lg-4 col-md-6 mb-5 mb-lg-0">
                <h5 class="text-primary mb-4">Quick Links</h5>
                <div class="row">
                    <div class="col-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="<?= base_url('/') ?>" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-angle-right text-primary me-2"></i>Home
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="<?= base_url('/dishes') ?>" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-angle-right text-primary me-2"></i>Menu
                                </a>
                            </li>
                            <?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
                                <li class="mb-2">
                                    <a href="<?= base_url('/booking/create') ?>" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                        <i class="fas fa-angle-right text-primary me-2"></i>Book Tiffin
                                    </a>
                                </li>
                            <?php endif; ?>
                            <li class="mb-2">
                                <a href="#about" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-angle-right text-primary me-2"></i>About Us
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="#contact" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-angle-right text-primary me-2"></i>Contact
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-angle-right text-primary me-2"></i>FAQ
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-angle-right text-primary me-2"></i>Privacy Policy
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="text-light-emphasis text-decoration-none d-flex align-items-center">
                                    <i class="fas fa-angle-right text-primary me-2"></i>Terms of Service
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Column 3: Contact Information -->
            <div class="col-lg-4 col-md-12 mb-4 mb-lg-0">
                <h5 class="text-primary mb-4">Contact Information</h5>
                <?php
                // Get contact settings with defaults
                $contactAddress = '123 Food Street, Delicious City, FC 12345';
                $contactPhone = '+91 9876543210';
                $contactEmail = '<EMAIL>';
                $businessHoursWeekday = '8:00 AM - 10:00 PM';
                $businessHoursSunday = '9:00 AM - 9:00 PM';

                try {
                    $query = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('contact_address', 'contact_phone', 'contact_email', 'business_hours_weekday', 'business_hours_sunday')");
                    $contactResults = $query->getResultArray();

                    foreach ($contactResults as $row) {
                        if (!empty($row['setting_value'])) {
                            switch ($row['setting_key']) {
                                case 'contact_address':
                                    $contactAddress = $row['setting_value'];
                                    break;
                                case 'contact_phone':
                                    $contactPhone = $row['setting_value'];
                                    break;
                                case 'contact_email':
                                    $contactEmail = $row['setting_value'];
                                    break;
                                case 'business_hours_weekday':
                                    $businessHoursWeekday = $row['setting_value'];
                                    break;
                                case 'business_hours_sunday':
                                    $businessHoursSunday = $row['setting_value'];
                                    break;
                            }
                        }
                    }
                } catch (Exception $e) {
                    // Use defaults
                }
                ?>

                <div class="contact-info">
                    <!-- Address -->
                    <div class="d-flex align-items-start mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-map-marker-alt text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1 text-light">Address</h6>
                            <p class="mb-0 text-light-emphasis small"><?= nl2br($contactAddress) ?></p>
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="d-flex align-items-start mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-phone text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1 text-light">Phone</h6>
                            <a href="tel:<?= str_replace([' ', '-', '(', ')'], '', $contactPhone) ?>" class="text-light-emphasis text-decoration-none small">
                                <?= $contactPhone ?>
                            </a>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="d-flex align-items-start mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-envelope text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1 text-light">Email</h6>
                            <a href="mailto:<?= $contactEmail ?>" class="text-light-emphasis text-decoration-none small">
                                <?= $contactEmail ?>
                            </a>
                        </div>
                    </div>

                    <!-- Business Hours -->
                    <div class="d-flex align-items-start">
                        <div class="flex-shrink-0">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-clock text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1 text-light">Business Hours</h6>
                            <p class="mb-0 text-light-emphasis small">
                                <strong>Mon-Sat:</strong> <?= $businessHoursWeekday ?><br>
                                <strong>Sunday:</strong> <?= $businessHoursSunday ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom py-4 mt-4 border-top border-secondary">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0 text-light-emphasis">&copy; <?= date('Y') ?> <?= $siteName ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p class="mb-0 text-light-emphasis">
                        Designed with <i class="fas fa-heart text-danger"></i> for good food lovers
                    </p>
                </div>
            </div>
        </div>
    </div>
</footer>