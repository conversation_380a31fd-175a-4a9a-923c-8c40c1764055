<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users"></i> Manage Users</h1>
    <div class="d-flex align-items-center">
        <span class="text-muted me-3">Total: <?= $totalUsers ?? 0 ?> users</span>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" action="<?= base_url('/admin/users') ?>">
            <div class="row g-3 align-items-end">
                <div class="col-md-6">
                    <label for="search" class="form-label">Search Users</label>
                    <input type="text" class="form-control" id="search" name="search"
                        value="<?= esc($search ?? '') ?>"
                        placeholder="Search by name, email, or phone...">
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <a href="<?= base_url('/admin/users') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<?php if (empty($users)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> No users found.
    </div>
<?php else: ?>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Registered On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?= $user['id'] ?></td>
                                <td><?= $user['name'] ?></td>
                                <td><?= $user['email'] ?></td>
                                <td><?= $user['phone'] ?: 'Not provided' ?></td>
                                <td><?= date('M d, Y', strtotime($user['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('/admin/users/view/' . $user['id']) ?>" class="btn btn-sm btn-info text-white">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <button type="button" class="btn btn-sm btn-warning" onclick="showPasswordModal(<?= $user['id'] ?>, '<?= htmlspecialchars($user['name']) ?>')">
                                            <i class="fas fa-key"></i> Password
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if (isset($pager) && $pager->getPageCount() > 1): ?>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Showing <?= ($currentPage - 1) * $perPage + 1 ?> to
                    <?= min($currentPage * $perPage, $totalUsers) ?> of <?= $totalUsers ?> users
                </div>
                <div>
                    <?= $pager->links() ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>

<!-- Password Change Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="passwordModalLabel">
                    <i class="fas fa-key"></i> Change Password for <span id="userName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="passwordForm" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="modal_new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="modal_new_password" name="new_password" required minlength="6">
                        <div class="form-text">Password must be at least 6 characters long.</div>
                    </div>
                    <div class="mb-3">
                        <label for="modal_confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="modal_confirm_password" name="confirm_password" required minlength="6">
                    </div>
                    <div id="passwordError" class="alert alert-danger d-none"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key"></i> Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function showPasswordModal(userId, userName) {
        document.getElementById('userName').textContent = userName;
        document.getElementById('passwordForm').action = '<?= base_url('/admin/users/change-password/') ?>' + userId;
        document.getElementById('modal_new_password').value = '';
        document.getElementById('modal_confirm_password').value = '';
        document.getElementById('passwordError').classList.add('d-none');

        var modal = new bootstrap.Modal(document.getElementById('passwordModal'));
        modal.show();
    }

    document.getElementById('passwordForm').addEventListener('submit', function(e) {
        var newPassword = document.getElementById('modal_new_password').value;
        var confirmPassword = document.getElementById('modal_confirm_password').value;
        var errorDiv = document.getElementById('passwordError');

        // Reset error display
        errorDiv.classList.add('d-none');

        // Validation
        if (newPassword.length < 6) {
            errorDiv.textContent = 'Password must be at least 6 characters long.';
            errorDiv.classList.remove('d-none');
            e.preventDefault();
            return;
        }

        if (newPassword !== confirmPassword) {
            errorDiv.textContent = 'Passwords do not match.';
            errorDiv.classList.remove('d-none');
            e.preventDefault();
            return;
        }

        // Confirm action
        if (!confirm('Are you sure you want to change this user\'s password?')) {
            e.preventDefault();
            return;
        }
    });
</script>

<?= $this->endSection() ?>