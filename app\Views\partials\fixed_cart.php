<?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
    <!-- Fixed Cart Button -->
    <div class="fixed-cart">
        <button onclick="toggleCartSidebar()" class="btn btn-success">
            <i class="fas fa-shopping-cart"></i>
            <?php
            $cart = session()->get('cart');
            $guest_cart = session()->get('guest_cart');
            $totalItems = 0;

            // Calculate total items from both carts
            if (!empty($cart)) {
                $totalItems += array_sum(array_column($cart, 'quantity'));
            }
            if (!empty($guest_cart)) {
                $totalItems += array_sum(array_column($guest_cart, 'quantity'));
            }

            if ($totalItems > 0):
            ?>
                <span class="cart-count" id="fixed-cart-badge"><?= $totalItems ?></span>
            <?php endif; ?>
        </button>
    </div>
<?php endif; ?>
