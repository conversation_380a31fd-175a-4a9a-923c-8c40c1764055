<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-star"></i> Customer Reviews</h1>
    <div class="d-flex align-items-center">
        <span class="text-muted me-3">Total: <?= $totalReviews ?? 0 ?> reviews</span>
    </div>
</div>

<!-- Search Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" action="<?= base_url('/admin/reviews') ?>">
            <div class="row g-3 align-items-end">
                <div class="col-md-6">
                    <label for="search" class="form-label">Search Reviews</label>
                    <input type="text" class="form-control" id="search" name="search"
                        value="<?= esc($search ?? '') ?>"
                        placeholder="Search by customer name, comment, or rating...">
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <a href="<?= base_url('/admin/reviews') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">All Reviews</h6>
    </div>
    <div class="card-body">
        <?php if (empty($reviews)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No reviews found.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Customer</th>
                            <th>Rating</th>
                            <th>Comment</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($reviews as $review): ?>
                            <tr>
                                <td><?= $review['id'] ?></td>
                                <td><?= $review['user_name'] ?></td>
                                <td>
                                    <div class="rating-stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <?php if ($i <= $review['rating']): ?>
                                                <i class="fas fa-star text-warning"></i>
                                            <?php else: ?>
                                                <i class="far fa-star text-warning"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($review['comment'])): ?>
                                        <?= substr($review['comment'], 0, 50) . (strlen($review['comment']) > 50 ? '...' : '') ?>
                                    <?php else: ?>
                                        <span class="text-muted">No comment</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($review['created_at'])) ?></td>
                                <td>
                                    <a href="<?= base_url('/admin/reviews/view/' . $review['id']) ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url('/admin/reviews/delete/' . $review['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this review?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalReviews > $perPage): ?>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing <?= ($currentPage - 1) * $perPage + 1 ?> to
                        <?= min($currentPage * $perPage, $totalReviews) ?> of <?= $totalReviews ?> reviews
                    </div>
                    <div>
                        <?php
                        $totalPages = ceil($totalReviews / $perPage);
                        $searchParam = $search ? '&search=' . urlencode($search) : '';
                        ?>
                        <nav aria-label="Reviews pagination">
                            <ul class="pagination">
                                <!-- Previous Page -->
                                <?php if ($currentPage > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= base_url('/admin/reviews?page=' . ($currentPage - 1) . $searchParam) ?>">Previous</a>
                                    </li>
                                <?php else: ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">Previous</span>
                                    </li>
                                <?php endif; ?>

                                <!-- Page Numbers -->
                                <?php
                                $start = max(1, $currentPage - 2);
                                $end = min($totalPages, $currentPage + 2);

                                if ($start > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= base_url('/admin/reviews?page=1' . $searchParam) ?>">1</a>
                                    </li>
                                    <?php if ($start > 2): ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                    <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                                        <a class="page-link" href="<?= base_url('/admin/reviews?page=' . $i . $searchParam) ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    <?php endif; ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= base_url('/admin/reviews?page=' . $totalPages . $searchParam) ?>"><?= $totalPages ?></a>
                                    </li>
                                <?php endif; ?>

                                <!-- Next Page -->
                                <?php if ($currentPage < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= base_url('/admin/reviews?page=' . ($currentPage + 1) . $searchParam) ?>">Next</a>
                                    </li>
                                <?php else: ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">Next</span>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>