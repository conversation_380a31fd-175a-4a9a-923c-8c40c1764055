<?php

if (!function_exists('get_setting')) {
    /**
     * Get a setting value from the database
     * 
     * @param string $key The setting key
     * @param mixed $default Default value if setting not found
     * @return mixed
     */
    function get_setting($key, $default = null)
    {
        static $settings = null;

        if ($settings === null) {
            $settingModel = new \App\Models\SettingModel();
            $settings = $settingModel->getAllSettings();
        }

        return $settings[$key] ?? $default;
    }
}

if (!function_exists('get_all_settings')) {
    /**
     * Get all settings as an array
     * 
     * @return array
     */
    function get_all_settings()
    {
        static $settings = null;

        if ($settings === null) {
            $settingModel = new \App\Models\SettingModel();
            $settings = $settingModel->getAllSettings();
        }

        return $settings;
    }
}

if (!function_exists('site_logo')) {
    /**
     * Get the site logo URL
     * 
     * @return string
     */
    function site_logo()
    {
        $logo = get_setting('site_logo');
        if ($logo) {
            return base_url() . 'uploads/settings/' . $logo;
        }
        return null;
    }
}

if (!function_exists('site_favicon')) {
    /**
     * Get the site favicon URL
     * 
     * @return string
     */
    function site_favicon()
    {
        $favicon = get_setting('site_favicon');
        if ($favicon) {
            return base_url() . 'uploads/settings/' . $favicon;
        }
        return null;
    }
}

if (!function_exists('site_name')) {
    /**
     * Get the site name
     * 
     * @return string
     */
    function site_name()
    {
        return get_setting('site_name', 'Tiffin Delight');
    }
}

if (!function_exists('currency_symbol')) {
    /**
     * Get the currency symbol
     * 
     * @return string
     */
    function currency_symbol()
    {
        return get_setting('currency_symbol', '₹');
    }
}

if (!function_exists('format_currency')) {
    /**
     * Format a number as currency
     * 
     * @param float $amount
     * @return string
     */
    function format_currency($amount)
    {
        return currency_symbol() . number_format($amount, 2);
    }
}
