<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\PasswordResetModel;

class Auth extends BaseController
{
    protected $userModel;
    protected $passwordResetModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->passwordResetModel = new PasswordResetModel();
    }

    public function index()
    {
        return redirect()->to('/auth/login');
    }

    public function login()
    {
        if ($this->request->getMethod() === 'POST') {
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');

            $user = $this->userModel->where('email', $email)->first();

            if ($user && password_verify($password, $user['password'])) {
                $session = session();
                $userData = [
                    'user_id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'logged_in' => true,
                    'is_admin' => false
                ];

                $session->set($userData);

                // Merge guest cart with user cart
                $bookingController = new \App\Controllers\Booking();
                $bookingController->mergeGuestCart();

                return redirect()->to('/user/dashboard');
            } else {
                return redirect()->back()->with('error', 'Invalid email or password');
            }
        }

        return view('auth/login');
    }

    public function register()
    {
        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'email' => 'required|valid_email|is_unique[users.email]',
                'password' => 'required|min_length[6]',
                'confirm_password' => 'required|matches[password]',
                'phone' => 'permit_empty|numeric|min_length[10]',
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'email' => $this->request->getPost('email'),
                    'password' => $this->request->getPost('password'),
                    'phone' => $this->request->getPost('phone'),
                    'address' => $this->request->getPost('address'),
                ];

                $this->userModel->insert($data);

                return redirect()->to('/auth/login')->with('success', 'Registration successful. Please login.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }
        }

        return view('auth/register');
    }

    public function logout()
    {
        $session = session();
        $session->destroy();

        return redirect()->to('/auth/login');
    }

    public function forgotPassword()
    {
        if ($this->request->getMethod() === 'POST') {
            $email = $this->request->getPost('email');

            // Check if user exists
            $user = $this->userModel->where('email', $email)->first();

            if (!$user) {
                return redirect()->back()->with('error', 'No account found with that email address.');
            }

            // Generate reset token
            $token = $this->passwordResetModel->createToken($email);

            // Send reset email
            $this->sendPasswordResetEmail($email, $token);

            return redirect()->back()->with('success', 'Password reset link has been sent to your email address.');
        }

        return view('auth/forgot_password');
    }

    public function resetPassword($token = null)
    {
        if (!$token) {
            return redirect()->to('/auth/forgot-password')->with('error', 'Invalid reset token.');
        }

        // Verify token
        $resetData = $this->passwordResetModel->verifyToken($token);

        if (!$resetData) {
            return redirect()->to('/auth/forgot-password')->with('error', 'Invalid or expired reset token.');
        }

        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'password' => 'required|min_length[6]',
                'confirm_password' => 'required|matches[password]'
            ];

            if ($this->validate($rules)) {
                $password = $this->request->getPost('password');

                // Update user password
                $this->userModel->where('email', $resetData['email'])
                    ->set(['password' => password_hash($password, PASSWORD_DEFAULT)])
                    ->update();

                // Delete the reset token
                $this->passwordResetModel->deleteToken($token);

                return redirect()->to('/auth/login')->with('success', 'Password has been reset successfully. Please login with your new password.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }
        }

        $data['token'] = $token;
        return view('auth/reset_password', $data);
    }

    private function sendPasswordResetEmail($email, $token)
    {
        $emailService = \Config\Services::email();

        $resetLink = base_url('/auth/reset-password/' . $token);

        $message = "Hello,\n\n";
        $message .= "You have requested to reset your password for Tiffin Delight.\n\n";
        $message .= "Please click the following link to reset your password:\n";
        $message .= $resetLink . "\n\n";
        $message .= "This link will expire in 1 hour.\n\n";
        $message .= "If you did not request this password reset, please ignore this email.\n\n";
        $message .= "Best regards,\n";
        $message .= "Tiffin Delight Team";

        $emailService->setTo($email);
        $emailService->setFrom('<EMAIL>', 'Tiffin Delight');
        $emailService->setSubject('Password Reset Request');
        $emailService->setMessage($message);

        try {
            $emailService->send();
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Failed to send password reset email: ' . $e->getMessage());
            return false;
        }
    }
}
