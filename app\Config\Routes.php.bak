<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

// Load the system's routing file first, so that the app and ENVIRONMENT
// can override as needed.
if (file_exists(SYSTEMPATH . 'Config/Routes.php')) {
    require SYSTEMPATH . 'Config/Routes.php';
}

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(true);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Home::index');

// Auth Routes
$routes->get('/auth/login', 'Auth::login');
$routes->post('/auth/login', 'Auth::login');
$routes->get('/auth/register', 'Auth::register');
$routes->post('/auth/register', 'Auth::register');
$routes->get('/auth/logout', 'Auth::logout');

// Dish Routes
$routes->get('/dishes', 'Dishes::index');
$routes->get('/dishes/view/(:num)', 'Dishes::view/$1');

// Booking Routes
$routes->get('/booking', 'Booking::index');
$routes->get('/booking/create', 'Booking::create');
$routes->get('/booking/add-to-cart/(:num)', 'Booking::addToCart/$1');
$routes->get('/booking/remove-from-cart/(:num)', 'Booking::removeFromCart/$1');
$routes->get('/booking/clear-cart', 'Booking::clearCart');
$routes->get('/booking/checkout', 'Booking::checkout');
$routes->post('/booking/place-order', 'Booking::placeOrder');
$routes->get('/booking/update-quantity/(:num)/(:num)', 'Booking::updateQuantity/$1/$2');

// Review Routes
$routes->get('/review/create/(:num)', 'Review::create/$1');
$routes->post('/review/store', 'Review::store');
$routes->post('/review/store-dish-review', 'Review::storeDishReview');

// Admin Review Routes
$routes->get('/admin/reviews', 'Review::index');
$routes->get('/admin/reviews/view/(:num)', 'Review::view/$1');
$routes->get('/admin/reviews/delete/(:num)', 'Review::delete/$1');

// User Routes
$routes->get('/user/profile', 'User::profile');
$routes->post('/user/update-profile', 'User::updateProfile');
$routes->get('/user/bookings', 'User::bookings');
$routes->get('/user/bookings/view/(:num)', 'User::viewBooking/$1');
$routes->get('/user/bookings/cancel/(:num)', 'User::cancelBooking/$1');
$routes->get('/user/wallet', 'User::wallet');
$routes->get('/user/wallet/recharge', 'User::rechargeWallet');
$routes->post('/user/wallet/recharge', 'User::rechargeWallet');

// Admin Routes
$routes->get('/admin', 'Admin::index');
$routes->get('/admin/login', 'Admin::login');
$routes->post('/admin/login', 'Admin::login');
$routes->get('/admin/logout', 'Admin::logout');
$routes->get('/admin/users', 'Admin::users');
$routes->get('/admin/users/view/(:num)', 'Admin::viewUser/$1');
$routes->get('/admin/bookings', 'Admin::bookings');
$routes->get('/admin/bookings/view/(:num)', 'Admin::viewBooking/$1');
$routes->get('/admin/bookings/update-status/(:num)/(:alpha)', 'Admin::updateBookingStatus/$1/$2');
$routes->get('/admin/dishes', 'Admin::dishes');
$routes->get('/admin/dishes/create', 'Admin::createDish');
$routes->post('/admin/dishes/store', 'Admin::storeDish');
$routes->get('/admin/dishes/edit/(:num)', 'Admin::editDish/$1');
$routes->post('/admin/dishes/update/(:num)', 'Admin::updateDish/$1');
$routes->get('/admin/dishes/delete/(:num)', 'Admin::deleteDish/$1');
$routes->get('/admin/delivery-slots', 'Admin::deliverySlots');
$routes->get('/admin/delivery-slots/create', 'Admin::createDeliverySlot');
$routes->post('/admin/delivery-slots/store', 'Admin::storeDeliverySlot');
$routes->get('/admin/delivery-slots/edit/(:num)', 'Admin::editDeliverySlot/$1');
$routes->post('/admin/delivery-slots/update/(:num)', 'Admin::updateDeliverySlot/$1');
$routes->get('/admin/delivery-slots/delete/(:num)', 'Admin::deleteDeliverySlot/$1');
$routes->get('/admin/reports', 'Admin::reports');

// API Routes
$routes->group('api', function($routes) {
    // Auth API Routes
    $routes->post('auth/login', 'Api\Auth::login');
    $routes->post('auth/register', 'Api\Auth::register');
    
    // Protected API Routes
    $routes->group('', ['filter' => 'jwt'], function($routes) {
        // User Profile
        $routes->get('user/profile', 'Api\User::profile');
        $routes->post('user/update-profile', 'Api\User::updateProfile');
        
        // Dishes
        $routes->get('dishes', 'Api\Dishes::index');
        $routes->get('dishes/(:num)', 'Api\Dishes::view/$1');
        
        // Cart & Booking
        $routes->get('cart', 'Api\Cart::index');
        $routes->post('cart/add', 'Api\Cart::add');
        $routes->post('cart/update', 'Api\Cart::update');
        $routes->post('cart/remove', 'Api\Cart::remove');
        $routes->post('cart/clear', 'Api\Cart::clear');
        $routes->post('booking/place-order', 'Api\Booking::placeOrder');
        $routes->get('bookings', 'Api\Booking::index');
        $routes->get('bookings/(:num)', 'Api\Booking::view/$1');
        $routes->post('bookings/cancel/(:num)', 'Api\Booking::cancel/$1');
        
        // Reviews
        $routes->post('reviews/add', 'Api\Review::add');
        $routes->get('reviews/dish/(:num)', 'Api\Review::getDishReviews/$1');
        
        // Wallet
        $routes->get('wallet', 'Api\Wallet::index');
        $routes->post('wallet/recharge', 'Api\Wallet::recharge');
    });
});

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (file_exists(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
