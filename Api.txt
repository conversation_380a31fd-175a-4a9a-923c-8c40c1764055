


### Public API Endpoints (No Authentication):
- GET /api/test - Test endpoint (Api\Test::index)
- GET /api/debug - Debug endpoint (Api\Debug::index)
- POST /api/debug/login - Debug login (Api\Debug::login)
- POST /api/auth/login - User authentication (Api\Auth::login)
- POST /api/auth/register - User registration (Api\Auth::register)
- GET /api/dishes - List all dishes (Api\Dishes::index)
- GET /api/dishes/(:num) - Get single dish (Api\Dishes::view/$1)
- GET /api/reviews/dish/(:num) - Get dish reviews (Api\Review::getDishReviews/$1)
- GET /api/banners - List banners (Api\Banners::index)

### Protected API Endpoints (JWT Required):
- GET /api/user/profile - Get user profile (Api\User::profile)
- POST /api/user/update-profile - Update profile (Api\User::updateProfile)
- GET /api/cart - Get cart contents (Api\Cart::index)
- POST /api/cart/add - Add to cart (Api\Cart::add)
- POST /api/cart/update - Update cart (Api\Cart::update)
- POST /api/cart/remove - Remove from cart (Api\Cart::remove)
- POST /api/cart/clear - Clear cart (Api\Cart::clear)
- POST /api/booking/place-order - Place order (Api\Booking::placeOrder)
- GET /api/bookings - List bookings (Api\Booking::index)
- GET /api/bookings/(:num) - Get booking details (Api\Booking::view/$1)
- POST /api/bookings/cancel/(:num) - Cancel booking (Api\Booking::cancel/$1)
- POST /api/reviews/add - Add review (Api\Review::add)
- GET /api/wallet - Get wallet details (Api\Wallet::index)
- POST /api/wallet/recharge - Wallet recharge (Api\Wallet::recharge)

### CORS Handling:
- OPTIONS /api/auth/login - Preflight request handler
All protected endpoints require a valid JWT token in the Authorization header. The API routes are organized under the /api namespace and follow RESTful conventions.