<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<!-- User Dashboard Header -->
<div class="container-fluid bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-dark mb-3">
                    <i class="fas fa-tachometer-alt text-primary me-3"></i>Welcome Back, <?= $user['name'] ?>!
                </h1>
                <p class="lead text-muted mb-4">Manage your account, orders, and preferences</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('/') ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item active">Dashboard</li>
                    </ol>
                </nav>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex align-items-center justify-content-lg-end">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 80px; height: 80px;">
                        <span class="fw-bold" style="font-size: 2rem;"><?= strtoupper(substr($user['name'], 0, 1)) ?></span>
                    </div>
                    <div class="text-start">
                        <h4 class="mb-1 fw-bold text-dark"><?= $user['name'] ?></h4>
                        <p class="mb-0 text-muted"><?= $user['email'] ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div class="container py-5">
    <!-- User Navigation Menu -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/dashboard') ?>" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                                <span class="fw-bold">Dashboard</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/profile') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-user-edit fa-2x mb-2"></i>
                                <span class="fw-bold">Profile</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-list-alt fa-2x mb-2"></i>
                                <span class="fw-bold">My Bookings</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/wallet') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-wallet fa-2x mb-2"></i>
                                <span class="fw-bold">My Wallet</span>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-8 col-12">
                            <a href="<?= base_url('/booking/create') ?>" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span class="fw-bold">Book New Tiffin</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Account Overview Card -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Account Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php
                        $stats = [
                            [
                                'title' => 'Wallet Balance',
                                'value' => '₹' . number_format($wallet['balance'] ?? 0, 2),
                                'icon' => 'fas fa-wallet',
                                'color' => 'success'
                            ],
                            [
                                'title' => 'Total Bookings',
                                'value' => count($bookings),
                                'icon' => 'fas fa-list-alt',
                                'color' => 'primary'
                            ],
                            [
                                'title' => 'Confirmed Orders',
                                'value' => count(array_filter($bookings, function ($b) {
                                    return $b['status'] == 'confirmed';
                                })),
                                'icon' => 'fas fa-check-circle',
                                'color' => 'info'
                            ],
                            [
                                'title' => 'Pending Orders',
                                'value' => count(array_filter($bookings, function ($b) {
                                    return $b['status'] == 'pending';
                                })),
                                'icon' => 'fas fa-clock',
                                'color' => 'warning'
                            ]
                        ];
                        ?>
                        <?php foreach ($stats as $stat): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <i class="<?= $stat['icon'] ?> fa-2x text-<?= $stat['color'] ?>"></i>
                                        </div>
                                        <h4 class="fw-bold text-<?= $stat['color'] ?>"><?= $stat['value'] ?></h4>
                                        <p class="text-muted mb-0"><?= $stat['title'] ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php
                        $actions = [
                            [
                                'title' => 'Book New Tiffin',
                                'url' => base_url('/booking/create'),
                                'icon' => 'fas fa-calendar-plus',
                                'color' => 'success'
                            ],
                            [
                                'title' => 'Browse Menu',
                                'url' => base_url('/dishes'),
                                'icon' => 'fas fa-utensils',
                                'color' => 'info'
                            ],
                            [
                                'title' => 'Recharge Wallet',
                                'url' => base_url('/user/wallet/recharge'),
                                'icon' => 'fas fa-plus-circle',
                                'color' => 'warning'
                            ],
                            [
                                'title' => 'Edit Profile',
                                'url' => base_url('/user/profile'),
                                'icon' => 'fas fa-user-edit',
                                'color' => 'secondary'
                            ]
                        ];
                        ?>
                        <?php foreach ($actions as $action): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <i class="<?= $action['icon'] ?> fa-2x text-<?= $action['color'] ?>"></i>
                                        </div>
                                        <a href="<?= $action['url'] ?>" class="btn btn-<?= $action['color'] ?> w-100">
                                            <?= $action['title'] ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Profile Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3 text-white fw-bold" style="width: 80px; height: 80px; font-size: 2rem;">
                            <?= strtoupper(substr($user['name'], 0, 1)) ?>
                        </div>
                        <h6 class="fw-bold mb-1"><?= $user['name'] ?></h6>
                        <small class="text-muted"><?= $user['email'] ?></small>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <p class="mb-2"><strong>Phone:</strong> <?= $user['phone'] ?: 'Not provided' ?></p>
                        <p class="mb-0"><strong>Address:</strong> <?= $user['address'] ? (strlen($user['address']) > 50 ? substr($user['address'], 0, 50) . '...' : $user['address']) : 'Not provided' ?></p>
                    </div>

                    <hr>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> Account Status</h6>
                        <p class="mb-0">Your account is active and ready to place orders!</p>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> Quick Tip</h6>
                        <p class="mb-0">Keep your wallet topped up for faster checkout experience.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Bookings Section -->
    <div class="row mt-4">
        <div class="col-12">

            <!-- Recent Bookings -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> Recent Bookings</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($bookings)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No Bookings Yet</h5>
                            <p class="text-muted mb-4">You haven't made any bookings yet. Start by exploring our delicious menu!</p>
                            <a href="<?= base_url('/booking/create') ?>" class="btn btn-success btn-lg">
                                <i class="fas fa-calendar-plus me-2"></i>Book Your First Tiffin
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Booking ID</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($bookings, 0, 5) as $booking): ?>
                                        <tr>
                                            <td><strong>#<?= $booking['id'] ?></strong></td>
                                            <td><?= date('M d, Y', strtotime($booking['booking_date'])) ?></td>
                                            <td><strong>₹<?= number_format($booking['total_amount'], 2) ?></strong></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusIcon = '';
                                                switch ($booking['status']) {
                                                    case 'pending':
                                                        $statusClass = 'warning';
                                                        $statusIcon = 'clock';
                                                        break;
                                                    case 'confirmed':
                                                        $statusClass = 'success';
                                                        $statusIcon = 'check-circle';
                                                        break;
                                                    case 'delivered':
                                                        $statusClass = 'info';
                                                        $statusIcon = 'check-double';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'danger';
                                                        $statusIcon = 'times-circle';
                                                        break;
                                                    default:
                                                        $statusClass = 'secondary';
                                                        $statusIcon = 'question-circle';
                                                }
                                                ?>
                                                <span class="badge bg-<?= $statusClass ?> px-3 py-2">
                                                    <i class="fas fa-<?= $statusIcon ?> me-1"></i><?= ucfirst($booking['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="<?= base_url('/user/bookings/view/' . $booking['id']) ?>" class="btn btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('/user/invoice/' . $booking['id']) ?>" class="btn btn-outline-success" title="View Invoice">
                                                        <i class="fas fa-file-invoice"></i>
                                                    </a>
                                                    <?php if ($booking['status'] == 'pending' || $booking['status'] == 'confirmed'): ?>
                                                        <a href="<?= base_url('/user/bookings/cancel/' . $booking['id']) ?>" class="btn btn-outline-danger" title="Cancel Booking" onclick="return confirm('Are you sure you want to cancel this booking?')">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (count($bookings) > 5): ?>
                            <div class="text-center mt-3">
                                <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-list me-2"></i>View All Bookings
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <style>
        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
        }

        .sticky-top {
            z-index: 100;
        }

        @media (max-width: 767.98px) {
            .sticky-top {
                position: relative;
                top: 0 !important;
            }
        }
    </style>
    <?= $this->endSection() ?>