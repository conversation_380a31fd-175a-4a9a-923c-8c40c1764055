<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?= $booking['id'] ?> - Tiffin Delight</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .invoice-header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info h2 {
            color: #007bff;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            color: #007bff;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .table th {
            background-color: #007bff !important;
            color: white !important;
        }
        
        .total-row {
            background-color: #f8f9fa !important;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-confirmed { background-color: #cce5ff; color: #004085; }
        .status-delivered { background-color: #d4edda; color: #155724; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        
        .payment-wallet { background-color: #d4edda; color: #155724; }
        .payment-razorpay { background-color: #cce5ff; color: #004085; }
        .payment-cash { background-color: #e2e3e5; color: #383d41; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Print Button -->
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> Print Invoice
            </button>
            <button onclick="window.close()" class="btn btn-secondary ms-2">
                Close
            </button>
        </div>

        <!-- Invoice Content -->
        <div class="invoice-header">
            <div class="row">
                <div class="col-md-6 company-info">
                    <h2>Tiffin Delight</h2>
                    <p class="mb-1"><strong>Address:</strong> 123 Food Street, Taste City</p>
                    <p class="mb-1"><strong>Phone:</strong> +91 98765 43210</p>
                    <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                    <p class="mb-0"><strong>GST No:</strong> 27AAAAA0000A1Z5</p>
                </div>
                <div class="col-md-6 text-end">
                    <h1 class="invoice-title">INVOICE</h1>
                    <p class="mb-1"><strong>Invoice No:</strong> INV-<?= str_pad($booking['id'], 6, '0', STR_PAD_LEFT) ?></p>
                    <p class="mb-1"><strong>Date:</strong> <?= date('F j, Y', strtotime($booking['created_at'])) ?></p>
                    <p class="mb-1"><strong>Delivery Date:</strong> <?= date('F j, Y', strtotime($booking['booking_date'])) ?></p>
                    <?php if ($slot): ?>
                        <p class="mb-0"><strong>Delivery Time:</strong> <?= $slot['slot_time'] ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Customer and Order Information -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h5 class="text-primary mb-3">Bill To:</h5>
                <p class="mb-1"><strong><?= $user['name'] ?></strong></p>
                <p class="mb-1"><?= $user['email'] ?></p>
                <p class="mb-0"><?= $user['phone'] ?></p>
            </div>
            <div class="col-md-6">
                <h5 class="text-primary mb-3">Order Information:</h5>
                <p class="mb-1">
                    <strong>Status:</strong>
                    <?php
                    $statusClass = '';
                    $statusText = '';
                    switch ($booking['status']) {
                        case 'pending':
                            $statusClass = 'status-pending';
                            $statusText = 'Pending';
                            break;
                        case 'confirmed':
                            $statusClass = 'status-confirmed';
                            $statusText = 'Confirmed';
                            break;
                        case 'delivered':
                            $statusClass = 'status-delivered';
                            $statusText = 'Delivered';
                            break;
                        case 'cancelled':
                            $statusClass = 'status-cancelled';
                            $statusText = 'Cancelled';
                            break;
                    }
                    ?>
                    <span class="status-badge <?= $statusClass ?>"><?= $statusText ?></span>
                </p>
                <p class="mb-0">
                    <strong>Payment Method:</strong>
                    <?php if ($booking['payment_method'] == 'wallet'): ?>
                        <span class="status-badge payment-wallet">Wallet</span>
                    <?php elseif ($booking['payment_method'] == 'razorpay'): ?>
                        <span class="status-badge payment-razorpay">Online Payment</span>
                    <?php else: ?>
                        <span class="status-badge payment-cash">Cash on Delivery</span>
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- Order Items Table -->
        <div class="mb-4">
            <h5 class="text-primary mb-3">Order Details:</h5>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Item</th>
                        <th class="text-center" style="width: 100px;">Quantity</th>
                        <th class="text-end" style="width: 120px;">Unit Price</th>
                        <th class="text-end" style="width: 120px;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $subtotal = 0;
                    foreach ($items as $item):
                        $itemTotal = $item['price'] * $item['quantity'];
                        $subtotal += $itemTotal;
                    ?>
                        <tr>
                            <td><strong><?= $item['dish_name'] ?></strong></td>
                            <td class="text-center"><?= $item['quantity'] ?></td>
                            <td class="text-end">₹<?= number_format($item['price'], 2) ?></td>
                            <td class="text-end">₹<?= number_format($itemTotal, 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-end"><strong>Subtotal:</strong></td>
                        <td class="text-end"><strong>₹<?= number_format($subtotal, 2) ?></strong></td>
                    </tr>
                    <tr>
                        <td colspan="3" class="text-end"><strong>Tax (0%):</strong></td>
                        <td class="text-end"><strong>₹0.00</strong></td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="3" class="text-end"><strong>Total Amount:</strong></td>
                        <td class="text-end"><strong>₹<?= number_format($booking['total_amount'], 2) ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Footer -->
        <div class="row mt-5">
            <div class="col-md-6">
                <h6 class="text-primary">Terms & Conditions:</h6>
                <ul class="small">
                    <li>All orders are subject to availability</li>
                    <li>Delivery charges may apply for certain areas</li>
                    <li>Cancellation allowed up to 2 hours before delivery</li>
                    <li>For any queries, contact customer support</li>
                </ul>
            </div>
            <div class="col-md-6 text-end">
                <p class="mb-1"><strong>Thank you for choosing Tiffin Delight!</strong></p>
                <p class="small text-muted">This is a computer-generated invoice.</p>
                <p class="small text-muted">Generated on: <?= date('F j, Y g:i A') ?></p>
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
