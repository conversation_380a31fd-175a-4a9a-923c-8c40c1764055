<?= $this->extend('layouts/user_dashboard') ?>

<?= $this->section('title') ?>My Wallet<?= $this->endSection() ?>

<?= $this->section('page_title') ?>My Wallet<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">My Wallet</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-wallet"></i> My Wallet</h1>
    <a href="<?= base_url('/user/wallet/recharge') ?>" class="btn btn-success">
        <i class="fas fa-plus-circle"></i> Recharge Wallet
    </a>
</div>

<?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> Your wallet has been recharged successfully!
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-wallet"></i> Wallet Balance</h5>
            </div>
            <div class="card-body text-center">
                <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 100px; height: 100px;">
                    <i class="fas fa-wallet text-success" style="font-size: 3rem;"></i>
                </div>
                <h1 class="display-4 fw-bold text-success">₹<?= number_format($wallet['balance'] ?? 0, 2) ?></h1>
                <p class="lead text-muted mb-4">Current Balance</p>
                <div class="row g-2">
                    <div class="col-12">
                        <a href="<?= base_url('/user/wallet/recharge') ?>" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plus-circle me-2"></i>Recharge Wallet
                        </a>
                    </div>
                    <div class="col-12">
                        <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-shopping-cart me-2"></i>Use for Booking
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-history"></i> Transaction History</h5>
            </div>
            <div class="card-body">
                <?php if (empty($transactions)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No transactions found.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?= date('M d, Y H:i', strtotime($transaction['created_at'])) ?></td>
                                        <td>
                                            <?php if ($transaction['type'] == 'credit'): ?>
                                                <span class="badge bg-success">Credit</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Debit</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($transaction['type'] == 'credit'): ?>
                                                <span class="text-success">+₹<?= number_format($transaction['amount'], 2) ?></span>
                                            <?php else: ?>
                                                <span class="text-danger">-₹<?= number_format($transaction['amount'], 2) ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $transaction['description'] ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
</style>
<?= $this->endSection() ?>