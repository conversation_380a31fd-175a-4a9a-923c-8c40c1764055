<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<!-- User Wallet Header -->
<div class="container-fluid bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-dark mb-3">
                    <i class="fas fa-wallet text-primary me-3"></i>My Wallet
                </h1>
                <p class="lead text-muted mb-4">Manage your wallet balance and transaction history</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('/') ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('/user/dashboard') ?>" class="text-decoration-none">Dashboard</a></li>
                        <li class="breadcrumb-item active">My Wallet</li>
                    </ol>
                </nav>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex align-items-center justify-content-lg-end">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 80px; height: 80px;">
                        <span class="fw-bold" style="font-size: 2rem;"><?= strtoupper(substr(session()->get('name'), 0, 1)) ?></span>
                    </div>
                    <div class="text-start">
                        <h4 class="mb-1 fw-bold text-dark"><?= session()->get('name') ?></h4>
                        <p class="mb-0 text-muted"><?= session()->get('email') ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Wallet Content -->
<div class="container py-5">
    <!-- User Navigation Menu -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/dashboard') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                                <span class="fw-bold">Dashboard</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/profile') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-user-edit fa-2x mb-2"></i>
                                <span class="fw-bold">Profile</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-list-alt fa-2x mb-2"></i>
                                <span class="fw-bold">My Bookings</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="<?= base_url('/user/wallet') ?>" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-wallet fa-2x mb-2"></i>
                                <span class="fw-bold">My Wallet</span>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-8 col-12">
                            <a href="<?= base_url('/booking/create') ?>" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center text-decoration-none py-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span class="fw-bold">Book New Tiffin</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> Your wallet has been recharged successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-wallet"></i> Wallet Balance</h5>
                </div>
                <div class="card-body text-center">
                    <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 100px; height: 100px;">
                        <i class="fas fa-wallet text-success" style="font-size: 3rem;"></i>
                    </div>
                    <h1 class="display-4 fw-bold text-success">₹<?= number_format($wallet['balance'] ?? 0, 2) ?></h1>
                    <p class="lead text-muted mb-4">Current Balance</p>
                    <div class="row g-2">
                        <div class="col-12">
                            <a href="<?= base_url('/user/wallet/recharge') ?>" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-plus-circle me-2"></i>Recharge Wallet
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="<?= base_url('/user/bookings') ?>" class="btn btn-outline-primary btn-lg w-100">
                                <i class="fas fa-shopping-cart me-2"></i>Use for Booking
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Transaction History</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($transactions)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No transactions found.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transactions as $transaction): ?>
                                        <tr>
                                            <td><?= date('M d, Y H:i', strtotime($transaction['created_at'])) ?></td>
                                            <td>
                                                <?php if ($transaction['type'] == 'credit'): ?>
                                                    <span class="badge bg-success">Credit</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Debit</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($transaction['type'] == 'credit'): ?>
                                                    <span class="text-success">+₹<?= number_format($transaction['amount'], 2) ?></span>
                                                <?php else: ?>
                                                    <span class="text-danger">-₹<?= number_format($transaction['amount'], 2) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $transaction['description'] ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <style>
        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
        }
    </style>
    <?= $this->endSection() ?>