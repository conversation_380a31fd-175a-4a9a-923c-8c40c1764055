<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<!-- Full Width Banner Slider - 350px Height -->
<section class="position-relative overflow-hidden" style="height: 350px;">
    <div id="heroCarousel" class="carousel slide h-100" data-bs-ride="carousel" data-bs-interval="5000">
        <div class="carousel-inner h-100">
            <!-- Default Hero Slide -->
            <div class="carousel-item active h-100">
                <div class="position-relative h-100" style="background: linear-gradient(135deg, rgba(25, 135, 84, 0.8) 0%, rgba(13, 110, 253, 0.8) 100%), url('https://images.unsplash.com/photo-1596797038530-2c107229654b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;">
                    <div class="container h-100">
                        <div class="row align-items-center h-100">
                            <div class="col-lg-8">
                                <h1 class="display-4 fw-bold text-white mb-3">
                                    Premium <span class="text-warning">Homemade</span> Tiffin Service
                                </h1>
                                <p class="lead text-white mb-4 opacity-90">
                                    Experience the finest homemade meals crafted with love and delivered fresh to your doorstep.
                                </p>
                                <div class="d-flex flex-wrap gap-3">
                                    <a href="<?= base_url('/dishes') ?>" class="btn btn-success btn-lg px-4">
                                        <i class="fas fa-utensils me-2"></i> Explore Menu
                                    </a>
                                    <?php if (!session()->get('logged_in')): ?>
                                        <a href="<?= base_url('/auth/register') ?>" class="btn btn-outline-light btn-lg px-4">
                                            <i class="fas fa-user-plus me-2"></i> Join Now
                                        </a>
                                    <?php else: ?>
                                        <a href="<?= base_url('/booking/create') ?>" class="btn btn-outline-light btn-lg px-4">
                                            <i class="fas fa-shopping-cart me-2"></i> Order Now
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Banner Slides from Database -->
            <?php if (!empty($banners)): ?>
                <?php foreach ($banners as $banner): ?>
                    <div class="carousel-item h-100">
                        <div class="position-relative h-100" style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.4) 100%), url('<?= base_url('/uploads/banners/' . $banner['image']) ?>') center/cover;">
                            <div class="container h-100">
                                <div class="row align-items-center h-100">
                                    <div class="col-12 text-center">
                                        <h1 class="display-4 fw-bold text-white mb-3">
                                            <?= $banner['title'] ?>
                                        </h1>
                                        <?php if ($banner['subtitle']): ?>
                                            <p class="lead text-white mb-4 opacity-90">
                                                <?= $banner['subtitle'] ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if ($banner['button_text'] && $banner['button_link']): ?>
                                            <a href="<?= base_url($banner['button_link']) ?>" class="btn btn-success btn-lg px-4">
                                                <?= $banner['button_text'] ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
        </button>

        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
            <?php if (!empty($banners)): ?>
                <?php foreach ($banners as $index => $banner): ?>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="<?= $index + 1 ?>"></button>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>


<section class="py-5 bg-white">
    <div class="container">

        <!-- Section Header -->
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-dark">Our Signature Dishes</h2>
            <p class="lead text-muted">Handcrafted with love, served with passion</p>
            <div class="bg-success mx-auto rounded-pill" style="width: 80px; height: 4px;"></div>
        </div>

        <?php if (empty($featured_dishes)): ?>
            <div class="text-center py-5">
                <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px;">
                    <i class="fas fa-utensils text-muted fs-1"></i>
                </div>
                <h4 class="text-dark">No dishes available</h4>
                <p class="text-muted">Check back soon for delicious new additions!</p>
            </div>
        <?php else: ?>
            <!-- Simple Product Grid - 3-4 items per row -->
            <div class="row g-4">
                <?php foreach ($featured_dishes as $index => $dish): ?>
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <div class="card border-0 shadow-sm h-100 overflow-hidden">

                            <!-- Dish Image -->
                            <div class="position-relative" style="height: 250px;">
                                <?php if ($dish['image']): ?>
                                    <img src="<?= base_url('/uploads/dishes/' . $dish['image']) ?>"
                                        class="card-img-top h-100 object-fit-cover" alt="<?= $dish['name'] ?>">
                                <?php else: ?>
                                    <?php
                                    $placeholders = [
                                        'https://images.unsplash.com/photo-1567337710282-00832b415979?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                                        'https://images.unsplash.com/photo-1589302168068-964664d93dc0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                                        'https://images.unsplash.com/photo-1505253758473-96b7015fcd40?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                                        'https://images.unsplash.com/photo-1585937421612-70a008356c36?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                                        'https://images.unsplash.com/photo-1631452180519-c014fe946bc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                                    ];
                                    $placeholderIndex = $dish['id'] % count($placeholders);
                                    $placeholder = $placeholders[$placeholderIndex];
                                    ?>
                                    <img src="<?= $placeholder ?>"
                                        class="card-img-top h-100 object-fit-cover" alt="<?= $dish['name'] ?>">
                                <?php endif; ?>

                                <!-- Status Badge -->
                                <div class="position-absolute top-0 start-0 m-2">
                                    <?php if ($dish['available']): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Available
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Sold Out
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Diet Badge -->
                                <div class="position-absolute top-0 end-0 m-2">
                                    <?php if (isset($dish['is_vegetarian'])): ?>
                                        <?php if ($dish['is_vegetarian']): ?>
                                            <span class="badge bg-success rounded-circle p-2" title="Vegetarian">
                                                <i class="fas fa-leaf"></i>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger rounded-circle p-2" title="Non-Vegetarian">
                                                <i class="fas fa-drumstick-bite"></i>
                                            </span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Dish Content -->
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h5 class="card-title fw-bold mb-0"><?= $dish['name'] ?></h5>
                                    <span class="badge bg-success bg-gradient fs-6 px-2 py-1">₹<?= number_format($dish['price'], 2) ?></span>
                                </div>

                                <div class="mb-2">
                                    <?php
                                    $db = \Config\Database::connect();
                                    $builder = $db->table('reviews');
                                    $builder->select('AVG(rating) as avg_rating, COUNT(*) as review_count');
                                    $builder->join('booking_items', 'booking_items.booking_id = reviews.booking_id');
                                    $builder->where('booking_items.dish_id', $dish['id']);
                                    $result = $builder->get()->getRowArray();

                                    $avgRating = round($result['avg_rating'] ?? 0, 1);
                                    $reviewCount = $result['review_count'] ?? 0;

                                    for ($i = 1; $i <= 5; $i++):
                                        if ($i <= floor($avgRating)): ?>
                                            <i class="fas fa-star text-warning"></i>
                                        <?php elseif ($i - 0.5 <= $avgRating): ?>
                                            <i class="fas fa-star-half-alt text-warning"></i>
                                        <?php else: ?>
                                            <i class="far fa-star text-warning"></i>
                                    <?php endif;
                                    endfor; ?>
                                    <small class="text-muted ms-1">(<?= $reviewCount > 0 ? $avgRating : 'New' ?>)</small>
                                </div>

                                <p class="card-text text-muted small"><?= substr($dish['description'], 0, 80) . (strlen($dish['description']) > 80 ? '...' : '') ?></p>
                            </div>

                            <div class="card-footer bg-transparent border-0 p-3 pt-0">
                                <div class="d-grid gap-2">
                                    <?php if ($dish['available']): ?>
                                        <a href="<?= base_url('/booking/add-to-cart/' . $dish['id']) ?>" class="btn btn-success">
                                            <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-secondary" disabled>
                                            <i class="fas fa-times me-2"></i>Sold Out
                                        </button>
                                    <?php endif; ?>
                                    <a href="<?= base_url('/dishes/view/' . $dish['id']) ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- View All Button -->
            <div class="text-center mt-5">
                <a href="<?= base_url('/dishes') ?>" class="btn btn-primary btn-lg px-5">
                    View All Menu Items <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Customer Reviews Section -->
<section class="py-5 bg-light">
    <div class="container">

        <!-- Section Header -->
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-dark">What Our Customers Say</h2>
            <p class="lead text-muted">Real reviews from real customers</p>
            <div class="bg-success mx-auto rounded-pill" style="width: 80px; height: 4px;"></div>
        </div>

        <!-- Reviews Grid -->
        <div class="row g-4">
            <?php if (empty($reviews)): ?>
                <!-- Default reviews if no reviews in database -->
                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <div class="text-success mb-3">
                                <i class="fas fa-quote-left fs-2"></i>
                            </div>
                            <p class="card-text mb-4">"The food is absolutely delicious and reminds me of home-cooked meals. The delivery is always on time and the staff is very friendly."</p>
                            <div class="d-flex align-items-center">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                    class="rounded-circle me-3" width="50" height="50" alt="Priya Sharma">
                                <div>
                                    <h6 class="mb-1 fw-bold">Priya Sharma</h6>
                                    <div class="text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <div class="text-success mb-3">
                                <i class="fas fa-quote-left fs-2"></i>
                            </div>
                            <p class="card-text mb-4">"I've been ordering from Tiffin Delight for the past 3 months and I'm extremely satisfied with their service. The food is fresh, tasty, and reasonably priced."</p>
                            <div class="d-flex align-items-center">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                    class="rounded-circle me-3" width="50" height="50" alt="Rahul Verma">
                                <div>
                                    <h6 class="mb-1 fw-bold">Rahul Verma</h6>
                                    <div class="text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <div class="text-success mb-3">
                                <i class="fas fa-quote-left fs-2"></i>
                            </div>
                            <p class="card-text mb-4">"As a busy professional, Tiffin Delight has been a lifesaver. The food is nutritious and delicious, and the wallet system makes payments so convenient!"</p>
                            <div class="d-flex align-items-center">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                    class="rounded-circle me-3" width="50" height="50" alt="Ananya Patel">
                                <div>
                                    <h6 class="mb-1 fw-bold">Ananya Patel</h6>
                                    <div class="text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Display actual reviews from database -->
                <?php foreach ($reviews as $index => $review): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body p-4">
                                <div class="text-success mb-3">
                                    <i class="fas fa-quote-left fs-2"></i>
                                </div>
                                <p class="card-text mb-4">
                                    <?php if (!empty($review['comment'])): ?>
                                        <?= nl2br($review['comment']) ?>
                                    <?php else: ?>
                                        Great food and service!
                                    <?php endif; ?>
                                </p>
                                <div class="d-flex align-items-center">
                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3 text-white fw-bold"
                                        style="width: 50px; height: 50px;">
                                        <?= substr($review['user_name'], 0, 1) ?>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 fw-bold"><?= $review['user_name'] ?></h6>
                                        <div class="text-warning">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <?php if ($i <= $review['rating']): ?>
                                                    <i class="fas fa-star"></i>
                                                <?php else: ?>
                                                    <i class="far fa-star"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-5">
            <?php if (session()->get('logged_in') && !session()->get('is_admin')): ?>
                <a href="<?= base_url('/user/bookings') ?>" class="btn btn-primary btn-lg px-5">
                    <i class="fas fa-star me-2"></i> Leave Your Review
                </a>
            <?php else: ?>
                <a href="<?= base_url('/auth/register') ?>" class="btn btn-primary btn-lg px-5">
                    <i class="fas fa-user-plus me-2"></i> Join Our Happy Customers
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-success text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="display-6 fw-bold mb-3">Ready to Order Your Tiffin?</h2>
                <p class="lead mb-0">Get delicious, homemade food delivered to your doorstep today!</p>
            </div>
            <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                <?php if (!session()->get('logged_in')): ?>
                    <a href="<?= base_url('/auth/register') ?>" class="btn btn-light btn-lg px-4">
                        <i class="fas fa-user-plus me-2"></i> Register Now
                    </a>
                <?php else: ?>
                    <a href="<?= base_url('/booking/create') ?>" class="btn btn-light btn-lg px-4">
                        <i class="fas fa-shopping-cart me-2"></i> Order Now
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?= $this->endSection() ?>