<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddMissingSettings extends Migration
{
    public function up()
    {
        // Add missing settings that weren't in the original migration
        $data = [
            [
                'setting_key' => 'youtube_url',
                'setting_value' => '',
                'setting_type' => 'url',
                'description' => 'YouTube Channel URL',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'business_hours_weekday',
                'setting_value' => '8:00 AM - 10:00 PM',
                'setting_type' => 'text',
                'description' => 'Business Hours for Weekdays (Mon-Sat)',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'business_hours_sunday',
                'setting_value' => '9:00 AM - 9:00 PM',
                'setting_type' => 'text',
                'description' => 'Business Hours for Sunday',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'google_analytics',
                'setting_value' => '',
                'setting_type' => 'text',
                'description' => 'Google Analytics Tracking ID',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Check if settings already exist before inserting
        foreach ($data as $setting) {
            $existing = $this->db->table('settings')
                ->where('setting_key', $setting['setting_key'])
                ->get()
                ->getRow();
            
            if (!$existing) {
                $this->db->table('settings')->insert($setting);
            }
        }
    }

    public function down()
    {
        // Remove the added settings
        $keys = [
            'youtube_url',
            'business_hours_weekday', 
            'business_hours_sunday',
            'google_analytics'
        ];

        foreach ($keys as $key) {
            $this->db->table('settings')->where('setting_key', $key)->delete();
        }
    }
}
