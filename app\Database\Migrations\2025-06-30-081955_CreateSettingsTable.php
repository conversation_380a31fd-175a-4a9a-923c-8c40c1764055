<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSettingsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'setting_key' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
            ],
            'setting_value' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'setting_type' => [
                'type'       => 'ENUM',
                'constraint' => ['text', 'textarea', 'email', 'url', 'file', 'number', 'select'],
                'default'    => 'text',
            ],
            'description' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('setting_key');
        $this->forge->createTable('settings');

        // Insert default settings
        $data = [
            [
                'setting_key' => 'site_name',
                'setting_value' => 'Tiffin Delight',
                'setting_type' => 'text',
                'description' => 'Website Name',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'site_logo',
                'setting_value' => '',
                'setting_type' => 'file',
                'description' => 'Website Logo',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'site_favicon',
                'setting_value' => '',
                'setting_type' => 'file',
                'description' => 'Website Favicon',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'contact_phone',
                'setting_value' => '+91 9876543210',
                'setting_type' => 'text',
                'description' => 'Contact Phone Number',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'contact_email',
                'setting_value' => '<EMAIL>',
                'setting_type' => 'email',
                'description' => 'Contact Email Address',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'contact_address',
                'setting_value' => '123 Food Street, Delicious City, FC 12345',
                'setting_type' => 'textarea',
                'description' => 'Contact Address',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'google_analytics',
                'setting_value' => '',
                'setting_type' => 'text',
                'description' => 'Google Analytics Tracking ID',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'facebook_url',
                'setting_value' => '',
                'setting_type' => 'url',
                'description' => 'Facebook Page URL',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'twitter_url',
                'setting_value' => '',
                'setting_type' => 'url',
                'description' => 'Twitter Profile URL',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'instagram_url',
                'setting_value' => '',
                'setting_type' => 'url',
                'description' => 'Instagram Profile URL',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'currency_symbol',
                'setting_value' => '₹',
                'setting_type' => 'text',
                'description' => 'Currency Symbol',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'currency_code',
                'setting_value' => 'INR',
                'setting_type' => 'text',
                'description' => 'Currency Code',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('settings')->insertBatch($data);
    }

    public function down()
    {
        $this->forge->dropTable('settings');
    }
}
