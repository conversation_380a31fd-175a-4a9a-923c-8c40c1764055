<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddNoticeSettings extends Migration
{
    public function up()
    {
        // Add notice settings
        $data = [
            [
                'setting_key' => 'notice_message',
                'setting_value' => 'We will Deliver Only 1 Km Area From Anora Kalan Only',
                'setting_type' => 'text',
                'description' => 'Notice message to display on home page',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'notice_enabled',
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'description' => 'Enable/disable notice bar on home page',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'notice_type',
                'setting_value' => 'warning',
                'setting_type' => 'text',
                'description' => 'Notice bar type (info, warning, danger, success)',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Check if settings already exist before inserting
        foreach ($data as $setting) {
            $existing = $this->db->table('settings')
                ->where('setting_key', $setting['setting_key'])
                ->get()
                ->getRow();
            
            if (!$existing) {
                $this->db->table('settings')->insert($setting);
            }
        }
    }

    public function down()
    {
        // Remove the added settings
        $keys = [
            'notice_message',
            'notice_enabled', 
            'notice_type'
        ];

        foreach ($keys as $key) {
            $this->db->table('settings')->where('setting_key', $key)->delete();
        }
    }
}
